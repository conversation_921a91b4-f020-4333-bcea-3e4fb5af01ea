// AIMS Student Management Components
// Comprehensive student management system with registration, management, promotions, and enrollments

// Uses global API services: window.StudentsAPI, window.ClassesAPI, etc.
// Uses global config: window.AIMSConfig
// Uses environment configuration: ../config/environment.js

const StudentManagementComponents = {
  // Component state
  state: {
    students: [],
    classes: [],
    subjects: [],
    academicYears: [],
    terms: [],
    combinations: [],
    currentPage: 'register-student',
    loading: false,
    filters: {
      class: '',
      status: '',
      search: ''
    }
  },

  // Initialize component
  async init() {
    await this.loadInitialData();
  },

  // Load initial data
  async loadInitialData() {
    try {
      this.state.loading = true;

      console.log('🔄 Loading student management data...');

      // Load all required data using API service with proper error handling
      const dataPromises = [
        this.loadStudentsData(),
        this.loadClassesData(),
        this.loadSubjectsData(),
        this.loadAcademicYearsData(),
        this.loadTermsData(),
        this.loadCombinationsData()
      ];

      await Promise.allSettled(dataPromises);

      console.log('✅ Student management data loaded successfully');

    } catch (error) {
      console.error('❌ Failed to load initial data:', error);
      if (window.AIMSConfig && window.AIMSConfig.get('development.debugMode')) {
        console.error('Debug: Initial data loading error details:', error);
      }
      // Show error notification if available
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
        window.AIMSDesignSystem.notifications.show('Failed to load data', 'error');
      }
    } finally {
      this.state.loading = false;
    }
  },

  // Load students data with error handling
  async loadStudentsData() {
    try {
      const result = await window.StudentsAPI.getAll();
      this.state.students = result;
      console.log('✅ Students data loaded:', result.data?.length || 0, 'students');
    } catch (error) {
      console.error('❌ Failed to load students:', error);
      this.state.students = { success: false, data: [] };
    }
  },

  // Load classes data with error handling
  async loadClassesData() {
    try {
      const result = await window.ClassesAPI.getAll();
      this.state.classes = result;
      console.log('✅ Classes data loaded:', result.data?.length || 0, 'classes');
    } catch (error) {
      console.error('❌ Failed to load classes:', error);
      this.state.classes = { success: false, data: [] };
    }
  },

  // Load subjects data with error handling
  async loadSubjectsData() {
    try {
      // Load both O-Level and A-Level subjects
      const [oLevelResult, aLevelResult] = await Promise.all([
        window.SubjectsAPI.oLevel.getAll(),
        window.SubjectsAPI.aLevel.getAll()
      ]);

      this.state.subjects = {
        success: true,
        data: {
          oLevel: oLevelResult.data || [],
          aLevel: aLevelResult.data || []
        }
      };
      console.log('✅ Subjects data loaded:',
        'O-Level:', oLevelResult.data?.length || 0,
        'A-Level:', aLevelResult.data?.length || 0);
    } catch (error) {
      console.error('❌ Failed to load subjects:', error);
      this.state.subjects = { success: false, data: { oLevel: [], aLevel: [] } };
    }
  },

  // Load academic years data with error handling
  async loadAcademicYearsData() {
    try {
      const result = await window.AcademicYearsAPI.getAll();
      this.state.academicYears = result;
      console.log('✅ Academic years data loaded:', result.data?.length || 0, 'years');
    } catch (error) {
      console.error('❌ Failed to load academic years:', error);
      this.state.academicYears = { success: false, data: [] };
    }
  },

  // Load terms data with error handling
  async loadTermsData() {
    try {
      const result = await window.TermsAPI.getAll();
      this.state.terms = result;
      console.log('✅ Terms data loaded:', result.data?.length || 0, 'terms');
    } catch (error) {
      console.error('❌ Failed to load terms:', error);
      this.state.terms = { success: false, data: [] };
    }
  },

  // Load combinations data with error handling
  async loadCombinationsData() {
    try {
      const result = await window.CombinationsAPI.getAll();
      this.state.combinations = result;
      console.log('✅ Combinations data loaded:', result.data?.length || 0, 'combinations');
    } catch (error) {
      console.error('❌ Failed to load combinations:', error);
      this.state.combinations = { success: false, data: [] };
    }
  }
};

// Register Student Component
const RegisterStudentComponent = {
  // Render register student form
  render() {
    return `
      <div class="space-y-6">
        ${AIMSDesignSystem.layouts.pageHeader(
          'Register Student',
          'Add new students to the system with their basic information and academic details'
        )}

        <!-- Registration Form -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <!-- Form Header -->
          <div class="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4">
            <h2 class="text-xl font-semibold text-white">Student Registration Form</h2>
            <p class="text-blue-100 text-sm mt-1">Complete all required fields to register a new student</p>
          </div>

          <form id="register-student-form" class="p-6 space-y-8">
            <!-- Hidden fields for system admin tracking -->
            <input type="hidden" id="registered_by_id" name="registered_by_id" value="">
            <input type="hidden" id="created_by_id" name="created_by_id" value="">
            <input type="hidden" id="updated_by_id" name="updated_by_id" value="">

            <!-- Personal Information Section -->
            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
              <div class="flex items-center mb-6">
                <div class="bg-blue-100 rounded-full p-3 mr-4">
                  <i class="fas fa-user text-blue-600 text-lg"></i>
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900">Personal Information</h3>
                  <p class="text-sm text-gray-600">Enter the student's basic personal details</p>
                </div>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                <!-- Admission Number - Full width on mobile, spans 2 cols on larger screens -->
                <div class="md:col-span-2 xl:col-span-1">
                  ${AIMSDesignSystem.forms.input('admission_number', 'Admission Number', '', {
                    required: true,
                    placeholder: 'e.g., 2024/001',
                    helpText: 'Unique student identifier'
                  })}
                </div>

                <!-- Names - Responsive grid -->
                ${AIMSDesignSystem.forms.input('first_name', 'First Name', '', {
                  required: true,
                  placeholder: 'Enter first name'
                })}
                ${AIMSDesignSystem.forms.input('middle_name', 'Middle Name', '', {
                  placeholder: 'Enter middle name (optional)'
                })}
                ${AIMSDesignSystem.forms.input('last_name', 'Last Name', '', {
                  required: true,
                  placeholder: 'Enter last name'
                })}

                <!-- Gender -->
                ${AIMSDesignSystem.forms.select('gender', 'Gender', [
                  { value: '', label: 'Select Gender' },
                  { value: 'Male', label: 'Male' },
                  { value: 'Female', label: 'Female' }
                ], '', { required: true })}

                <!-- Passport Photo - Full width on mobile -->
                <div class="md:col-span-2 xl:col-span-1">
                  ${AIMSDesignSystem.forms.fileUpload('passport_photo', 'Passport Photo', {
                    accept: 'image/*',
                    helpText: 'Upload student passport photo (JPG, PNG - Max 2MB)'
                  })}
                </div>
              </div>
            </div>

            <!-- Academic Enrollment Section -->
            <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg p-6 border border-green-200">
              <div class="flex items-center mb-6">
                <div class="bg-green-100 rounded-full p-3 mr-4">
                  <i class="fas fa-graduation-cap text-green-600 text-lg"></i>
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900">Academic Enrollment</h3>
                  <p class="text-sm text-gray-600">Select the academic year, term, and class for enrollment</p>
                </div>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                <!-- Academic Year - Auto-populated and readonly -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    Academic Year <span class="text-red-500">*</span>
                  </label>
                  <div class="relative">
                    <input type="text" id="academic_year_display" readonly
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600 cursor-not-allowed"
                           placeholder="Loading active academic year...">
                    <input type="hidden" id="academic_year_id" name="academic_year_id" required>
                    <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                      <i class="fas fa-lock text-gray-400"></i>
                    </div>
                  </div>
                  <p class="text-xs text-gray-500 mt-1">Current active academic year (auto-selected)</p>
                </div>

                <!-- Term - Auto-populated and readonly -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    Term <span class="text-red-500">*</span>
                  </label>
                  <div class="relative">
                    <input type="text" id="term_display" readonly
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600 cursor-not-allowed"
                           placeholder="Loading active term...">
                    <input type="hidden" id="term_id" name="term_id" required>
                    <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                      <i class="fas fa-lock text-gray-400"></i>
                    </div>
                  </div>
                  <p class="text-xs text-gray-500 mt-1">Current active term (auto-selected)</p>
                </div>

                <!-- Class Selection -->
                ${AIMSDesignSystem.forms.select('class_id', 'Class Level', [], '', {
                  required: true,
                  helpText: 'Select the student\'s class level (S.1-S.6)'
                })}
              </div>

            </div>

            <!-- O-Level Subject Selection Section -->
            <div id="o-level-subjects-section" class="hidden bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg p-6 border border-green-200">
              <div class="flex items-center mb-6">
                <div class="bg-green-100 rounded-full p-3 mr-4">
                  <i class="fas fa-book-open text-green-600 text-lg"></i>
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900">O-Level Subject Selection</h3>
                  <p class="text-sm text-gray-600">Select subjects for O-Level student (S.1 - S.4)</p>
                </div>
              </div>
              <div id="o-level-subjects-container" class="min-h-[100px]">
                <!-- O-Level subjects will be populated dynamically -->
              </div>
            </div>

            <!-- A-Level Combination & Subject Selection Section -->
            <div id="a-level-section" class="hidden space-y-6">
              <!-- A-Level Combination Selection -->
              <div class="bg-gradient-to-br from-yellow-50 to-amber-50 rounded-lg p-6 border border-yellow-200">
                <div class="flex items-center mb-6">
                  <div class="bg-yellow-100 rounded-full p-3 mr-4">
                    <i class="fas fa-layer-group text-yellow-600 text-lg"></i>
                  </div>
                  <div>
                    <h3 class="text-lg font-semibold text-gray-900">A-Level Subject Combination</h3>
                    <p class="text-sm text-gray-600">Select the subject combination for A-Level student (S.5 - S.6)</p>
                  </div>
                </div>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    ${AIMSDesignSystem.forms.select('combination_id', 'Subject Combination', [], '', {
                      required: false,
                      helpText: 'Select the A-Level subject combination'
                    })}
                  </div>
                  <div class="bg-yellow-100 rounded-lg p-4">
                    <div class="flex items-start">
                      <i class="fas fa-info-circle text-yellow-600 mr-2 mt-0.5"></i>
                      <div class="text-sm text-yellow-800">
                        <p class="font-medium mb-1">Important:</p>
                        <p>A-Level students must select a subject combination that determines their stream:</p>
                        <ul class="list-disc list-inside mt-2 space-y-1">
                          <li><strong>Sciences Stream:</strong> Math, Physics, Chemistry, Biology, etc.</li>
                          <li><strong>Arts Stream:</strong> History, Geography, Literature, etc.</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- A-Level Subject Selection -->
              <div id="a-level-subjects-section" class="hidden bg-gradient-to-br from-purple-50 to-violet-50 rounded-lg p-6 border border-purple-200">
                <div class="flex items-center mb-6">
                  <div class="bg-purple-100 rounded-full p-3 mr-4">
                    <i class="fas fa-graduation-cap text-purple-600 text-lg"></i>
                  </div>
                  <div>
                    <h3 class="text-lg font-semibold text-gray-900">A-Level Subject Selection</h3>
                    <p class="text-sm text-gray-600">Select subjects based on the chosen combination</p>
                  </div>
                </div>
                <div id="a-level-subjects-container" class="min-h-[100px]">
                  <!-- A-Level subjects will be populated dynamically -->
                </div>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-6 border border-gray-200">
              <div class="flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0 sm:space-x-4">
                <div class="text-sm text-gray-600 flex items-center">
                  <i class="fas fa-info-circle mr-2 text-blue-500"></i>
                  All required fields must be completed before registration
                </div>
                <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4 w-full sm:w-auto">
                  ${AIMSDesignSystem.forms.button('cancel', 'Cancel', 'secondary', {
                    type: 'button',
                    onclick: 'RegisterStudentComponent.cancel()',
                    icon: 'fas fa-times',
                    class: 'w-full sm:w-auto'
                  })}
                  ${AIMSDesignSystem.forms.button('register', 'Register Student', 'primary', {
                    type: 'submit',
                    loading: false,
                    icon: 'fas fa-user-plus',
                    class: 'w-full sm:w-auto'
                  })}
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    `;
  },

  // Initialize register student component
  async init() {
    await StudentManagementComponents.loadInitialData();
    this.populateDropdowns();
    this.populateSystemAdminFields();
    this.initializeEventListeners();
  },

  // Populate system admin fields with current logged-in admin ID
  populateSystemAdminFields() {
    // Get current logged-in admin ID from localStorage or auth token
    const currentAdminId = this.getCurrentAdminId();

    if (currentAdminId) {
      const registeredByField = document.getElementById('registered_by_id');
      const createdByField = document.getElementById('created_by_id');
      const updatedByField = document.getElementById('updated_by_id');

      if (registeredByField) registeredByField.value = currentAdminId;
      if (createdByField) createdByField.value = currentAdminId;
      if (updatedByField) updatedByField.value = currentAdminId;
    }
  },

  // Get current admin ID from authentication
  getCurrentAdminId() {
    // Use the standardized audit fields utility
    if (window.AuditFieldsUtil) {
      return window.AuditFieldsUtil.getCurrentUserId();
    }

    // Fallback implementation
    try {
      if (window.AIMS && window.AIMS.currentUser && window.AIMS.currentUser.id) {
        return window.AIMS.currentUser.id;
      }
      return 1; // Default to system admin
    } catch (error) {
      console.error('Error getting current admin ID:', error);
      return 1;
    }
  },

  // Populate dropdown fields
  populateDropdowns() {
    // Auto-populate active academic year (readonly)
    const academicYearDisplay = document.getElementById('academic_year_display');
    const academicYearHidden = document.getElementById('academic_year_id');

    if (academicYearDisplay && academicYearHidden) {
      const academicYears = StudentManagementComponents.state.academicYears.data || StudentManagementComponents.state.academicYears;
      if (Array.isArray(academicYears)) {
        const activeYear = academicYears.find(year => year.is_active);
        if (activeYear) {
          academicYearDisplay.value = activeYear.name;
          academicYearHidden.value = activeYear.id;
          console.log('✅ Active academic year set:', activeYear.name);
        } else {
          academicYearDisplay.value = 'No active academic year found';
          academicYearDisplay.classList.add('text-red-600');
        }
      }
    }

    // Auto-populate active term (readonly)
    const termDisplay = document.getElementById('term_display');
    const termHidden = document.getElementById('term_id');

    if (termDisplay && termHidden) {
      const terms = StudentManagementComponents.state.terms.data || StudentManagementComponents.state.terms;
      if (Array.isArray(terms)) {
        const activeTerm = terms.find(term => term.is_active);
        if (activeTerm) {
          termDisplay.value = activeTerm.name;
          termHidden.value = activeTerm.id;
          console.log('✅ Active term set:', activeTerm.name);
        } else {
          termDisplay.value = 'No active term found';
          termDisplay.classList.add('text-red-600');
        }
      }
    }

    // Populate classes
    const classSelect = document.getElementById('class_id');
    if (classSelect) {
      classSelect.innerHTML = '<option value="">Select Class Level</option>';
      const classes = StudentManagementComponents.state.classes.data || StudentManagementComponents.state.classes;
      if (Array.isArray(classes)) {
        // Group classes by education level for better organization
        const oLevelClasses = classes.filter(cls =>
          cls.education_level_code === 'o_level' ||
          cls.class_level_code?.startsWith('s') && ['s1', 's2', 's3', 's4'].includes(cls.class_level_code) ||
          cls.name?.match(/S\.[1-4]/)
        );
        const aLevelClasses = classes.filter(cls =>
          cls.education_level_code === 'a_level' ||
          cls.class_level_code?.startsWith('s') && ['s5', 's6'].includes(cls.class_level_code) ||
          cls.name?.match(/S\.[5-6]/)
        );

        // Add O-Level classes
        if (oLevelClasses.length > 0) {
          const oLevelGroup = document.createElement('optgroup');
          oLevelGroup.label = 'O-Level Classes (S.1 - S.4)';
          oLevelClasses.forEach(cls => {
            const option = document.createElement('option');
            option.value = cls.id;
            option.textContent = cls.name + (cls.stream_name ? ` - ${cls.stream_name}` : '');
            option.dataset.educationLevel = 'o_level';
            oLevelGroup.appendChild(option);
          });
          classSelect.appendChild(oLevelGroup);
        }

        // Add A-Level classes
        if (aLevelClasses.length > 0) {
          const aLevelGroup = document.createElement('optgroup');
          aLevelGroup.label = 'A-Level Classes (S.5 - S.6)';
          aLevelClasses.forEach(cls => {
            const option = document.createElement('option');
            option.value = cls.id;
            option.textContent = cls.name + (cls.stream_name ? ` - ${cls.stream_name}` : '');
            option.dataset.educationLevel = 'a_level';
            aLevelGroup.appendChild(option);
          });
          classSelect.appendChild(aLevelGroup);
        }

        // Add any remaining classes that don't fit the pattern
        const otherClasses = classes.filter(cls =>
          !oLevelClasses.includes(cls) && !aLevelClasses.includes(cls)
        );
        otherClasses.forEach(cls => {
          const option = document.createElement('option');
          option.value = cls.id;
          option.textContent = cls.name + (cls.stream_name ? ` - ${cls.stream_name}` : '');
          classSelect.appendChild(option);
        });
      }
    }

    // Populate A-Level combinations
    const combinationSelect = document.getElementById('combination_id');
    if (combinationSelect) {
      combinationSelect.innerHTML = '<option value="">Select Subject Combination</option>';
      const combinations = StudentManagementComponents.state.combinations.data || StudentManagementComponents.state.combinations;
      if (Array.isArray(combinations)) {
        // Group combinations by type
        const sciencesCombinations = combinations.filter(c => c.combination_type === 'sciences');
        const artsCombinations = combinations.filter(c => c.combination_type === 'arts');
        const mixedCombinations = combinations.filter(c => c.combination_type === 'mixed');

        // Add Sciences combinations
        if (sciencesCombinations.length > 0) {
          const sciencesGroup = document.createElement('optgroup');
          sciencesGroup.label = 'Sciences Combinations';
          sciencesCombinations.forEach(combination => {
            const option = document.createElement('option');
            option.value = combination.id;
            option.textContent = combination.name;
            sciencesGroup.appendChild(option);
          });
          combinationSelect.appendChild(sciencesGroup);
        }

        // Add Arts combinations
        if (artsCombinations.length > 0) {
          const artsGroup = document.createElement('optgroup');
          artsGroup.label = 'Arts Combinations';
          artsCombinations.forEach(combination => {
            const option = document.createElement('option');
            option.value = combination.id;
            option.textContent = combination.name;
            artsGroup.appendChild(option);
          });
          combinationSelect.appendChild(artsGroup);
        }

        // Add Mixed combinations
        if (mixedCombinations.length > 0) {
          const mixedGroup = document.createElement('optgroup');
          mixedGroup.label = 'Mixed Combinations';
          mixedCombinations.forEach(combination => {
            const option = document.createElement('option');
            option.value = combination.id;
            option.textContent = combination.name;
            mixedGroup.appendChild(option);
          });
          combinationSelect.appendChild(mixedGroup);
        }
      }
    }
  },

  // Initialize event listeners
  initializeEventListeners() {
    // Handle form submission
    const form = document.getElementById('register-student-form');
    if (form) {
      form.addEventListener('submit', this.handleSubmit.bind(this));
    }

    // Handle class selection change
    const classSelect = document.getElementById('class_id');
    if (classSelect) {
      classSelect.addEventListener('change', this.handleClassChange.bind(this));
    }

    // Handle combination selection change
    const combinationSelect = document.getElementById('combination_id');
    if (combinationSelect) {
      combinationSelect.addEventListener('change', this.handleCombinationChange.bind(this));
    }
  },

  // Handle class selection change
  handleClassChange(event) {
    const classId = event.target.value;
    const selectedOption = event.target.selectedOptions[0];
    const classes = StudentManagementComponents.state.classes.data || StudentManagementComponents.state.classes;
    const selectedClass = Array.isArray(classes) ? classes.find(cls => cls.id == classId) : null;

    console.log('🎯 Class selection changed:', { classId, selectedClass });

    // Hide all sections first
    this.hideAllSubjectSections();

    if (selectedClass || selectedOption) {
      // Determine education level from option data or class object
      const educationLevel = selectedOption?.dataset?.educationLevel ||
                           selectedClass?.education_level_code ||
                           this.determineEducationLevelFromClass(selectedClass);

      console.log('📚 Education level determined:', educationLevel);

      if (educationLevel === 'a_level') {
        console.log('📚 A-Level class selected - showing A-Level sections');
        this.showALevelSections();
      } else if (educationLevel === 'o_level') {
        console.log('📖 O-Level class selected - showing O-Level sections');
        this.showOLevelSections(classId);
      }
    }
  },

  // Determine education level from class information
  determineEducationLevelFromClass(selectedClass) {
    if (!selectedClass) return null;

    // Check various ways to determine if it's A-Level or O-Level
    if (selectedClass.education_level_code === 'a_level' ||
        selectedClass.class_level_code === 's5' ||
        selectedClass.class_level_code === 's6' ||
        selectedClass.name?.includes('S.5') ||
        selectedClass.name?.includes('S.6')) {
      return 'a_level';
    } else if (selectedClass.education_level_code === 'o_level' ||
               ['s1', 's2', 's3', 's4'].includes(selectedClass.class_level_code) ||
               selectedClass.name?.match(/S\.[1-4]/)) {
      return 'o_level';
    }

    return null;
  },

  // Hide all subject selection sections
  hideAllSubjectSections() {
    const oLevelSection = document.getElementById('o-level-subjects-section');
    const aLevelSection = document.getElementById('a-level-section');
    const aLevelSubjectsSection = document.getElementById('a-level-subjects-section');

    if (oLevelSection) oLevelSection.classList.add('hidden');
    if (aLevelSection) aLevelSection.classList.add('hidden');
    if (aLevelSubjectsSection) aLevelSubjectsSection.classList.add('hidden');

    // Clear selections
    const combinationSelect = document.getElementById('combination_id');
    if (combinationSelect) combinationSelect.value = '';

    this.clearSubjectContainers();
  },

  // Show O-Level subject selection
  showOLevelSections() {
    const oLevelSection = document.getElementById('o-level-subjects-section');
    if (oLevelSection) {
      oLevelSection.classList.remove('hidden');
      this.loadOLevelSubjects();
    }
  },

  // Show A-Level combination and subject selection
  showALevelSections() {
    const aLevelSection = document.getElementById('a-level-section');
    if (aLevelSection) {
      aLevelSection.classList.remove('hidden');
    }
  },

  // Clear all subject containers
  clearSubjectContainers() {
    const oLevelContainer = document.getElementById('o-level-subjects-container');
    const aLevelContainer = document.getElementById('a-level-subjects-container');

    if (oLevelContainer) oLevelContainer.innerHTML = '';
    if (aLevelContainer) aLevelContainer.innerHTML = '';
  },

  // Handle combination selection change
  handleCombinationChange(event) {
    const combinationId = event.target.value;
    if (combinationId) {
      this.loadALevelSubjectsForCombination(combinationId);
    } else {
      // Hide A-Level subjects section if no combination selected
      const aLevelSubjectsSection = document.getElementById('a-level-subjects-section');
      if (aLevelSubjectsSection) aLevelSubjectsSection.classList.add('hidden');

      const aLevelContainer = document.getElementById('a-level-subjects-container');
      if (aLevelContainer) aLevelContainer.innerHTML = '';
    }
  },

  // Load O-Level subjects for the selected class
  async loadOLevelSubjects() {
    try {
      console.log('📚 Loading O-Level subjects...');

      // Use the subjects API to get O-Level subjects
      const result = await window.SubjectsAPI.oLevel.getAll();

      if (result.success) {
        const subjects = result.data || result;
        console.log('✅ O-Level subjects loaded:', subjects.length);
        this.renderOLevelSubjects(Array.isArray(subjects) ? subjects : []);
      } else {
        console.error('Failed to load O-Level subjects:', result.message);
        this.showSubjectLoadError('o-level-subjects-container', 'O-Level subjects');
      }
    } catch (error) {
      console.error('Failed to load O-Level subjects:', error);
      this.showSubjectLoadError('o-level-subjects-container', 'O-Level subjects');
    }
  },

  // Load A-Level subjects for selected combination
  async loadALevelSubjectsForCombination(combinationId) {
    try {
      console.log('📚 Loading A-Level subjects for combination:', combinationId);

      // Use the API service to get A-Level combination subjects
      const result = await window.CombinationsAPI.getSubjects(combinationId);

      if (result.success) {
        const subjects = result.data || result;
        this.renderALevelSubjects(Array.isArray(subjects) ? subjects : []);

        // Show the A-Level subjects section
        const aLevelSubjectsSection = document.getElementById('a-level-subjects-section');
        if (aLevelSubjectsSection) aLevelSubjectsSection.classList.remove('hidden');
      } else {
        console.error('Failed to load combination subjects:', result.message);
        this.showSubjectLoadError('a-level-subjects-container', 'A-Level combination subjects');
      }
    } catch (error) {
      console.error('Failed to load combination subjects:', error);
      this.showSubjectLoadError('a-level-subjects-container', 'A-Level combination subjects');
    }
  },

  // Show error message when subjects fail to load
  showSubjectLoadError(containerId, subjectType) {
    const container = document.getElementById(containerId);
    if (container) {
      container.innerHTML = `
        <div class="text-center py-12 text-red-500">
          <div class="bg-red-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-exclamation-triangle text-2xl text-red-500"></i>
          </div>
          <p class="text-lg font-medium text-red-600">Failed to load ${subjectType}</p>
          <p class="text-sm text-red-500 mt-1">Please try refreshing the page or contact support.</p>
        </div>
      `;
    }
  },

  // Render O-Level subjects
  renderOLevelSubjects(subjects) {
    const container = document.getElementById('o-level-subjects-container');
    if (!container) return;

    if (!Array.isArray(subjects) || subjects.length === 0) {
      container.innerHTML = `
        <div class="text-center py-12 text-gray-500">
          <div class="bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-book text-2xl text-gray-400"></i>
          </div>
          <p class="text-lg font-medium text-gray-600">No O-Level subjects available</p>
          <p class="text-sm text-gray-500 mt-1">Please contact the administrator.</p>
        </div>
      `;
      return;
    }

    // Group O-Level subjects by type
    const compulsorySubjects = subjects.filter(s => s.subject_type === 'Compulsory');
    const languageSubjects = subjects.filter(s => s.subject_type === 'Language');
    const practicalSubjects = subjects.filter(s => s.subject_type === 'Practical (pre-vocational)');
    const religiousSubjects = subjects.filter(s => s.subject_type === 'Religious Education');

    container.innerHTML = `
      <div class="space-y-6">
        ${compulsorySubjects.length > 0 ? `
          <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
            <h4 class="font-semibold text-blue-900 mb-3 flex items-center">
              <i class="fas fa-star text-blue-600 mr-2"></i>
              Compulsory Subjects
            </h4>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              ${compulsorySubjects.map(subject => this.renderSubjectCard(subject, true)).join('')}
            </div>
          </div>
        ` : ''}

        ${languageSubjects.length > 0 ? `
          <div class="bg-green-50 rounded-lg p-4 border border-green-200">
            <h4 class="font-semibold text-green-900 mb-3 flex items-center">
              <i class="fas fa-language text-green-600 mr-2"></i>
              Language Subjects
            </h4>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              ${languageSubjects.map(subject => this.renderSubjectCard(subject)).join('')}
            </div>
          </div>
        ` : ''}

        ${practicalSubjects.length > 0 ? `
          <div class="bg-purple-50 rounded-lg p-4 border border-purple-200">
            <h4 class="font-semibold text-purple-900 mb-3 flex items-center">
              <i class="fas fa-tools text-purple-600 mr-2"></i>
              Practical Subjects
            </h4>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              ${practicalSubjects.map(subject => this.renderSubjectCard(subject)).join('')}
            </div>
          </div>
        ` : ''}

        ${religiousSubjects.length > 0 ? `
          <div class="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
            <h4 class="font-semibold text-yellow-900 mb-3 flex items-center">
              <i class="fas fa-pray text-yellow-600 mr-2"></i>
              Religious Education
            </h4>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              ${religiousSubjects.map(subject => this.renderSubjectCard(subject)).join('')}
            </div>
          </div>
        ` : ''}
      </div>
    `;
  },

  // Render A-Level subjects
  renderALevelSubjects(subjects) {
    const container = document.getElementById('a-level-subjects-container');
    if (!container) return;

    if (!Array.isArray(subjects) || subjects.length === 0) {
      container.innerHTML = `
        <div class="text-center py-12 text-gray-500">
          <div class="bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-book text-2xl text-gray-400"></i>
          </div>
          <p class="text-lg font-medium text-gray-600">No subjects available</p>
          <p class="text-sm text-gray-500 mt-1">Please select a valid combination first.</p>
        </div>
      `;
      return;
    }

    // Group subjects by type
    const principalSubjects = subjects.filter(s => s.subject_type === 'Principal' || s.subject_type === 'principal');
    const subsidiarySubjects = subjects.filter(s => s.subject_type === 'Subsidiary' || s.subject_type === 'subsidiary');
    const compulsorySubjects = subjects.filter(s => s.is_compulsory || s.subject_type === 'compulsory');
    const otherSubjects = subjects.filter(s =>
      !principalSubjects.includes(s) &&
      !subsidiarySubjects.includes(s) &&
      !compulsorySubjects.includes(s)
    );

    container.innerHTML = `
      <div class="space-y-6">
        ${compulsorySubjects.length > 0 ? `
          <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
            <h4 class="font-semibold text-blue-900 mb-3 flex items-center">
              <i class="fas fa-star text-blue-600 mr-2"></i>
              Compulsory Subjects
            </h4>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              ${compulsorySubjects.map(subject => this.renderSubjectCard(subject, true)).join('')}
            </div>
          </div>
        ` : ''}

        ${principalSubjects.length > 0 ? `
          <div class="bg-green-50 rounded-lg p-4 border border-green-200">
            <h4 class="font-semibold text-green-900 mb-3 flex items-center">
              <i class="fas fa-graduation-cap text-green-600 mr-2"></i>
              Principal Subjects
            </h4>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              ${principalSubjects.map(subject => this.renderSubjectCard(subject)).join('')}
            </div>
          </div>
        ` : ''}

        ${subsidiarySubjects.length > 0 ? `
          <div class="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
            <h4 class="font-semibold text-yellow-900 mb-3 flex items-center">
              <i class="fas fa-book text-yellow-600 mr-2"></i>
              Subsidiary Subjects
            </h4>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              ${subsidiarySubjects.map(subject => this.renderSubjectCard(subject)).join('')}
            </div>
          </div>
        ` : ''}

        ${otherSubjects.length > 0 ? `
          <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
            <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
              <i class="fas fa-list text-gray-600 mr-2"></i>
              Other Subjects
            </h4>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              ${otherSubjects.map(subject => this.renderSubjectCard(subject)).join('')}
            </div>
          </div>
        ` : ''}
      </div>
    `;
  },

  // Render individual subject card
  renderSubjectCard(subject, isCompulsory = false) {
    const isRequired = isCompulsory || subject.is_compulsory || subject.subject_type === 'compulsory';

    return `
      <div class="flex items-center space-x-3 p-3 bg-white border border-gray-200 rounded-lg hover:border-gray-300 transition-colors">
        <input type="checkbox"
               id="subject_${subject.id}"
               name="selected_subjects[]"
               value="${subject.id}"
               ${isRequired ? 'checked disabled' : ''}
               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
        <label for="subject_${subject.id}" class="flex-1 cursor-pointer">
          <div class="font-medium text-gray-900 text-sm">${subject.name}</div>
          <div class="text-xs text-gray-500">${subject.short_name || subject.code || ''}</div>
          ${isRequired ?
            '<span class="inline-block text-xs text-blue-600 font-medium bg-blue-100 px-2 py-1 rounded mt-1">Required</span>' :
            subject.subject_type ? `<span class="inline-block text-xs text-green-600 font-medium bg-green-100 px-2 py-1 rounded mt-1">${subject.subject_type}</span>` : ''
          }
        </label>
      </div>
    `;
  },

  // Handle form submission
  async handleSubmit(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    let data = Object.fromEntries(formData.entries());

    // Get current admin ID for audit fields
    const currentAdminId = this.getCurrentAdminId();

    // Add audit fields using the utility
    if (window.AuditFieldsUtil) {
      data = window.AuditFieldsUtil.addAuditFieldsToData(data, false); // false = new record
      // Also set registered_by_id for student-specific tracking
      data.registered_by_id = window.AuditFieldsUtil.getCurrentUserId();
    } else {
      // Fallback
      if (currentAdminId) {
        data.registered_by_id = currentAdminId;
        data.created_by_id = currentAdminId;
      }
    }

    // Validate required fields
    if (!data.admission_number || !data.first_name || !data.last_name || !data.gender) {
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
        window.AIMSDesignSystem.notifications.show('Please fill in all required personal information fields', 'error');
      }
      return;
    }

    if (!data.academic_year_id || !data.term_id || !data.class_id) {
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
        window.AIMSDesignSystem.notifications.show('Please select academic year, term, and class', 'error');
      }
      return;
    }

    // Collect selected subjects for A-Level students
    const selectedSubjects = Array.from(document.querySelectorAll('input[name="selected_subjects[]"]:checked'))
      .map(input => parseInt(input.value));

    data.selected_subjects = selectedSubjects;

    // Prepare enrollment data for student_enrollments table
    data.enrollment_data = {
      class_id: parseInt(data.class_id),
      academic_year_id: parseInt(data.academic_year_id),
      term_id: parseInt(data.term_id),
      combination_id: data.combination_id ? parseInt(data.combination_id) : null,
      enrollment_date: new Date().toISOString().split('T')[0], // Current date
      enrolled_by_id: currentAdminId,
      status: 'active',
      enrollment_notes: `Student registered on ${new Date().toLocaleDateString()}`
    };

    console.log('📝 Submitting student registration:', {
      student: {
        admission_number: data.admission_number,
        name: `${data.first_name} ${data.last_name}`,
        gender: data.gender
      },
      enrollment: data.enrollment_data,
      subjects: selectedSubjects.length
    });

    try {
      AIMSDesignSystem.forms.setButtonLoading('register', true);

      const result = await window.StudentsAPI.register(data);

      if (result.success) {
        // Show success notification
        if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
          window.AIMSDesignSystem.notifications.show(
            `Student ${data.first_name} ${data.last_name} registered successfully!`,
            'success'
          );
        }

        // Reset form
        event.target.reset();
        this.resetFormSections();

        // Redirect to manage students after a short delay
        setTimeout(() => {
          if (window.PageRouter) {
            window.PageRouter.loadPage('manage-students');
          }
        }, 2000);
      } else {
        // Show error notification
        if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
          window.AIMSDesignSystem.notifications.show(
            result.message || 'Failed to register student',
            'error'
          );
        }
      }
    } catch (error) {
      console.error('Registration error:', error);
      if (window.AIMSConfig && window.AIMSConfig.get('development.debugMode')) {
        console.error('Debug: Registration error details:', error);
      }
      // Show error notification
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
        window.AIMSDesignSystem.notifications.show(
          'Failed to register student. Please check your connection and try again.',
          'error'
        );
      }
    } finally {
      AIMSDesignSystem.forms.setButtonLoading('register', false);
    }
  },

  // Reset form sections to initial state
  resetFormSections() {
    // Hide all subject sections
    this.hideAllSubjectSections();

    // Reset class selection
    const classSelect = document.getElementById('class_id');
    if (classSelect) classSelect.value = '';

    // Reset combination selection
    const combinationSelect = document.getElementById('combination_id');
    if (combinationSelect) combinationSelect.value = '';
  },

  // Cancel registration
  cancel() {
    if (confirm('Are you sure you want to cancel? All entered data will be lost.')) {
      if (window.PageRouter) {
        window.PageRouter.loadPage('manage-students');
      }
    }
  }
};

// Manage Students Component
const ManageStudentsComponent = {
  // Render manage students interface
  render() {
    return `
      <div class="space-y-6">
        ${AIMSDesignSystem.layouts.pageHeader(
          'Manage Students',
          'View, edit, and manage student records and enrollments',
          [
            { icon: 'fas fa-users', value: StudentManagementComponents.state.students.data?.length || 0, label: 'Total Students', color: 'blue' },
            { icon: 'fas fa-user-check', value: this.getActiveStudentsCount(), label: 'Active', color: 'green' }
          ]
        )}

        <!-- Filters and Search -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <div class="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Search & Filter Students</h3>
            <p class="text-sm text-gray-600 mt-1">Use the filters below to find specific students</p>
          </div>
          <div class="p-6">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
              <div class="sm:col-span-2 lg:col-span-1">
                ${AIMSDesignSystem.forms.input('search', 'Search Students', '', {
                  placeholder: 'Name or admission number...',
                  icon: 'fas fa-search'
                })}
              </div>
              ${AIMSDesignSystem.forms.select('filter_class', 'Filter by Class', this.getClassOptions(), '')}
              ${AIMSDesignSystem.forms.select('filter_status', 'Filter by Status', [
                { value: '', label: 'All Status' },
                { value: 'active', label: 'Active' },
                { value: 'transferred', label: 'Transferred' },
                { value: 'graduated', label: 'Graduated' },
                { value: 'dropped', label: 'Dropped' },
                { value: 'suspended', label: 'Suspended' }
              ], '')}
              <div class="flex flex-col sm:flex-row items-stretch sm:items-end space-y-2 sm:space-y-0 sm:space-x-2">
                ${AIMSDesignSystem.forms.button('add-student', 'Add Student', 'primary', {
                  icon: 'fas fa-plus',
                  onclick: 'ManageStudentsComponent.addStudent()',
                  class: 'w-full sm:w-auto'
                })}
                ${AIMSDesignSystem.forms.button('export', 'Export', 'secondary', {
                  icon: 'fas fa-download',
                  onclick: 'ManageStudentsComponent.exportStudents()',
                  class: 'w-full sm:w-auto'
                })}
              </div>
            </div>
          </div>
        </div>

        <!-- Students Table -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <div class="bg-gradient-to-r from-blue-50 to-blue-100 px-6 py-4 border-b border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
              <div>
                <h3 class="text-lg font-semibold text-gray-900">Student Records</h3>
                <p class="text-sm text-gray-600 mt-1">Manage all registered students</p>
              </div>
              <div class="mt-3 sm:mt-0">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                  <i class="fas fa-users mr-1"></i>
                  ${StudentManagementComponents.state.students.data?.length || 0} Students
                </span>
              </div>
            </div>
          </div>

          <!-- Mobile Card View (Hidden on larger screens) -->
          <div id="students-mobile-view" class="block lg:hidden">
            <!-- Mobile cards will be populated here -->
          </div>

          <!-- Desktop Table View (Hidden on mobile) -->
          <div class="hidden lg:block overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div class="flex items-center space-x-1">
                      <i class="fas fa-user text-gray-400"></i>
                      <span>Student</span>
                    </div>
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div class="flex items-center space-x-1">
                      <i class="fas fa-id-card text-gray-400"></i>
                      <span>Admission No.</span>
                    </div>
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div class="flex items-center space-x-1">
                      <i class="fas fa-door-open text-gray-400"></i>
                      <span>Class</span>
                    </div>
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div class="flex items-center space-x-1">
                      <i class="fas fa-circle text-gray-400"></i>
                      <span>Status</span>
                    </div>
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div class="flex items-center space-x-1">
                      <i class="fas fa-calendar text-gray-400"></i>
                      <span>Registration Date</span>
                    </div>
                  </th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div class="flex items-center justify-end space-x-1">
                      <i class="fas fa-cog text-gray-400"></i>
                      <span>Actions</span>
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody id="students-table-body" class="bg-white divide-y divide-gray-200">
                <!-- Students will be populated here -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    `;
  },

  // Initialize manage students component
  async init() {
    await StudentManagementComponents.loadInitialData();
    this.populateStudentsTable();
    this.initializeEventListeners();
  },

  // Get active students count
  getActiveStudentsCount() {
    const students = StudentManagementComponents.state.students.data || StudentManagementComponents.state.students;
    return Array.isArray(students) ? students.filter(s => s.status === 'active').length : 0;
  },

  // Get class options for filter
  getClassOptions() {
    const options = [{ value: '', label: 'All Classes' }];
    const classes = StudentManagementComponents.state.classes.data || StudentManagementComponents.state.classes;
    if (Array.isArray(classes)) {
      classes.forEach(cls => {
        options.push({
          value: cls.id,
          label: cls.name + (cls.stream_name ? ` - ${cls.stream_name}` : '')
        });
      });
    }
    return options;
  },

  // Populate students table
  populateStudentsTable() {
    const tbody = document.getElementById('students-table-body');
    const mobileView = document.getElementById('students-mobile-view');

    const students = this.getFilteredStudents();

    if (students.length === 0) {
      // Desktop empty state
      if (tbody) {
        tbody.innerHTML = `
          <tr>
            <td colspan="6" class="px-6 py-12 text-center text-gray-500">
              <i class="fas fa-users text-4xl mb-4 text-gray-300"></i>
              <p class="text-lg font-medium">No students found</p>
              <p class="text-sm">Try adjusting your search criteria or add new students.</p>
            </td>
          </tr>
        `;
      }

      // Mobile empty state
      if (mobileView) {
        mobileView.innerHTML = `
          <div class="p-8 text-center text-gray-500">
            <i class="fas fa-users text-4xl mb-4 text-gray-300"></i>
            <p class="text-lg font-medium">No students found</p>
            <p class="text-sm">Try adjusting your search criteria or add new students.</p>
          </div>
        `;
      }
      return;
    }

    // Desktop table view
    if (tbody) {
      tbody.innerHTML = students.map(student => `
        <tr class="hover:bg-gray-50 transition-colors duration-150">
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="flex items-center">
              <div class="flex-shrink-0 h-10 w-10">
                <img class="h-10 w-10 rounded-full object-cover border-2 border-gray-200"
                     src="${student.passport_photo || '/assets/images/default-avatar.png'}"
                     alt="${student.first_name} ${student.last_name}"
                     onerror="this.src='/assets/images/default-avatar.png'">
              </div>
              <div class="ml-4">
                <div class="text-sm font-medium text-gray-900">
                  ${student.first_name} ${student.middle_name || ''} ${student.last_name}
                </div>
                <div class="text-sm text-gray-500 flex items-center">
                  <i class="fas fa-${student.gender === 'Male' ? 'mars' : 'venus'} mr-1"></i>
                  ${student.gender}
                </div>
              </div>
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm font-medium text-gray-900">${student.admission_number}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-900">${student.class_name || 'Not Enrolled'}</div>
            ${student.stream_name ? `<div class="text-xs text-gray-500">${student.stream_name}</div>` : ''}
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${this.getStatusBadgeClass(student.status)}">
              <span class="w-1.5 h-1.5 mr-1.5 rounded-full ${this.getStatusDotClass(student.status)}"></span>
              ${student.status}
            </span>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
            ${student.created_at ? new Date(student.created_at).toLocaleDateString() : 'N/A'}
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
            <div class="flex items-center justify-end space-x-1">
              <button onclick="ManageStudentsComponent.viewStudent(${student.id})"
                      class="inline-flex items-center p-2 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded-lg transition-colors duration-150"
                      title="View Details">
                <i class="fas fa-eye"></i>
              </button>
              <button onclick="ManageStudentsComponent.editStudent(${student.id})"
                      class="inline-flex items-center p-2 text-indigo-600 hover:text-indigo-900 hover:bg-indigo-50 rounded-lg transition-colors duration-150"
                      title="Edit">
                <i class="fas fa-edit"></i>
              </button>
              <button onclick="ManageStudentsComponent.enrollStudent(${student.id})"
                      class="inline-flex items-center p-2 text-green-600 hover:text-green-900 hover:bg-green-50 rounded-lg transition-colors duration-150"
                      title="Enroll">
                <i class="fas fa-user-plus"></i>
              </button>
              <button onclick="ManageStudentsComponent.deleteStudent(${student.id})"
                      class="inline-flex items-center p-2 text-red-600 hover:text-red-900 hover:bg-red-50 rounded-lg transition-colors duration-150"
                      title="Delete">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </td>
        </tr>
      `).join('');
    }

    // Mobile card view
    if (mobileView) {
      mobileView.innerHTML = students.map(student => `
        <div class="border-b border-gray-200 p-4 hover:bg-gray-50 transition-colors duration-150">
          <div class="flex items-start space-x-4">
            <div class="flex-shrink-0">
              <img class="h-12 w-12 rounded-full object-cover border-2 border-gray-200"
                   src="${student.passport_photo || '/assets/images/default-avatar.png'}"
                   alt="${student.first_name} ${student.last_name}"
                   onerror="this.src='/assets/images/default-avatar.png'">
            </div>
            <div class="flex-1 min-w-0">
              <div class="flex items-center justify-between">
                <h4 class="text-sm font-medium text-gray-900 truncate">
                  ${student.first_name} ${student.middle_name || ''} ${student.last_name}
                </h4>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${this.getStatusBadgeClass(student.status)}">
                  <span class="w-1 h-1 mr-1 rounded-full ${this.getStatusDotClass(student.status)}"></span>
                  ${student.status}
                </span>
              </div>
              <div class="mt-1 flex items-center text-sm text-gray-500">
                <i class="fas fa-id-card mr-1"></i>
                <span class="mr-4">${student.admission_number}</span>
                <i class="fas fa-${student.gender === 'Male' ? 'mars' : 'venus'} mr-1"></i>
                <span>${student.gender}</span>
              </div>
              <div class="mt-1 flex items-center text-sm text-gray-500">
                <i class="fas fa-door-open mr-1"></i>
                <span class="mr-4">${student.class_name || 'Not Enrolled'}</span>
                ${student.stream_name ? `<span class="text-xs bg-gray-100 px-2 py-1 rounded">${student.stream_name}</span>` : ''}
              </div>
              <div class="mt-3 flex items-center justify-between">
                <span class="text-xs text-gray-500">
                  <i class="fas fa-calendar mr-1"></i>
                  ${student.created_at ? new Date(student.created_at).toLocaleDateString() : 'N/A'}
                </span>
                <div class="flex space-x-2">
                  <button onclick="ManageStudentsComponent.viewStudent(${student.id})"
                          class="inline-flex items-center p-1.5 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded"
                          title="View">
                    <i class="fas fa-eye text-xs"></i>
                  </button>
                  <button onclick="ManageStudentsComponent.editStudent(${student.id})"
                          class="inline-flex items-center p-1.5 text-indigo-600 hover:text-indigo-900 hover:bg-indigo-50 rounded"
                          title="Edit">
                    <i class="fas fa-edit text-xs"></i>
                  </button>
                  <button onclick="ManageStudentsComponent.enrollStudent(${student.id})"
                          class="inline-flex items-center p-1.5 text-green-600 hover:text-green-900 hover:bg-green-50 rounded"
                          title="Enroll">
                    <i class="fas fa-user-plus text-xs"></i>
                  </button>
                  <button onclick="ManageStudentsComponent.deleteStudent(${student.id})"
                          class="inline-flex items-center p-1.5 text-red-600 hover:text-red-900 hover:bg-red-50 rounded"
                          title="Delete">
                    <i class="fas fa-trash text-xs"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      `).join('');
    }
  },

  // Get filtered students based on search and filters
  getFilteredStudents() {
    const studentsData = StudentManagementComponents.state.students.data || StudentManagementComponents.state.students;
    let students = Array.isArray(studentsData) ? studentsData : [];

    // Apply search filter
    const searchTerm = document.getElementById('search')?.value.toLowerCase() || '';
    if (searchTerm) {
      students = students.filter(student =>
        student.first_name.toLowerCase().includes(searchTerm) ||
        student.last_name.toLowerCase().includes(searchTerm) ||
        student.admission_number.toLowerCase().includes(searchTerm)
      );
    }

    // Apply class filter
    const classFilter = document.getElementById('filter_class')?.value || '';
    if (classFilter) {
      students = students.filter(student => student.class_id == classFilter);
    }

    // Apply status filter
    const statusFilter = document.getElementById('filter_status')?.value || '';
    if (statusFilter) {
      students = students.filter(student => student.status === statusFilter);
    }

    return students;
  },

  // Get status badge CSS class
  getStatusBadgeClass(status) {
    const classes = {
      'active': 'bg-green-100 text-green-800',
      'transferred': 'bg-blue-100 text-blue-800',
      'graduated': 'bg-purple-100 text-purple-800',
      'dropped': 'bg-red-100 text-red-800',
      'suspended': 'bg-yellow-100 text-yellow-800'
    };
    return classes[status] || 'bg-gray-100 text-gray-800';
  },

  // Get status dot CSS class
  getStatusDotClass(status) {
    const classes = {
      'active': 'bg-green-400',
      'transferred': 'bg-blue-400',
      'graduated': 'bg-purple-400',
      'dropped': 'bg-red-400',
      'suspended': 'bg-yellow-400'
    };
    return classes[status] || 'bg-gray-400';
  },

  // Initialize event listeners
  initializeEventListeners() {
    // Search input
    const searchInput = document.getElementById('search');
    if (searchInput) {
      searchInput.addEventListener('input', () => {
        this.populateStudentsTable();
      });
    }

    // Filter dropdowns
    ['filter_class', 'filter_status'].forEach(filterId => {
      const filterElement = document.getElementById(filterId);
      if (filterElement) {
        filterElement.addEventListener('change', () => {
          this.populateStudentsTable();
        });
      }
    });
  },

  // Add new student
  addStudent() {
    if (window.PageRouter) {
      window.PageRouter.loadPage('register-student');
    }
  },

  // View student details
  viewStudent(studentId) {
    // Implementation for viewing student details
    console.log('View student:', studentId);
  },

  // Edit student
  editStudent(studentId) {
    // Implementation for editing student
    console.log('Edit student:', studentId);
  },

  // Enroll student
  enrollStudent(studentId) {
    // Implementation for enrolling student
    console.log('Enroll student:', studentId);
  },

  // Delete student
  async deleteStudent(studentId) {
    if (!confirm('Are you sure you want to delete this student? This action cannot be undone.')) {
      return;
    }

    try {
      const result = await window.StudentsAPI.delete(studentId);

      if (result.success) {
        // Show success notification using AIMSDesignSystem
        if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
          window.AIMSDesignSystem.notifications.show('Student deleted successfully', 'success');
        } else {
          alert('Student deleted successfully');
        }
        await StudentManagementComponents.loadInitialData();
        this.populateStudentsTable();
      } else {
        // Show error notification using AIMSDesignSystem
        if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
          window.AIMSDesignSystem.notifications.show(result.message || 'Failed to delete student', 'error');
        } else {
          alert('Error: ' + (result.message || 'Failed to delete student'));
        }
      }
    } catch (error) {
      console.error('Delete error:', error);
      if (window.AIMSConfig && window.AIMSConfig.get('development.debugMode')) {
        console.error('Debug: Delete error details:', error);
      }
      // Show error notification using AIMSDesignSystem
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
        window.AIMSDesignSystem.notifications.show('Failed to delete student', 'error');
      } else {
        alert('Error: Failed to delete student');
      }
    }
  },

  // Export students
  exportStudents() {
    // Implementation for exporting students data
    console.log('Export students');
  }
};


// Student Enrollment Component
const StudentEnrollmentComponent = {
  // Render student enrollment interface
  render() {
    return `
      <div class="space-y-6">
        ${AIMSDesignSystem.layouts.pageHeader(
          'Student Enrollment',
          'Enroll students in classes and manage their academic assignments'
        )}

        <!-- Enrollment Form -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
          <form id="enrollment-form" class="space-y-6">
            <!-- Hidden fields for system admin tracking -->
            <input type="hidden" id="enrollment_created_by_id" name="created_by_id" value="">
            <input type="hidden" id="enrollment_enrolled_by_id" name="enrolled_by_id" value="">
            <input type="hidden" id="enrollment_updated_by_id" name="updated_by_id" value="">

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              ${AIMSDesignSystem.forms.select('student_id', 'Select Student', [], '', { required: true })}
              ${AIMSDesignSystem.forms.select('academic_year_id', 'Academic Year', [], '', { required: true })}
              ${AIMSDesignSystem.forms.select('term_id', 'Term', [], '', { required: true })}
              ${AIMSDesignSystem.forms.select('class_id', 'Class', [], '', { required: true })}
              ${AIMSDesignSystem.forms.select('combination_id', 'A-Level Combination', [], '')}
              ${AIMSDesignSystem.forms.select('previous_class_id', 'Previous Class', [], '')}
            </div>

            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
              ${AIMSDesignSystem.forms.button('cancel', 'Cancel', 'secondary', { type: 'button' })}
              ${AIMSDesignSystem.forms.button('enroll', 'Enroll Student', 'primary', { type: 'submit' })}
            </div>
          </form>
        </div>

        <!-- Current Enrollments -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Current Enrollments</h3>
          </div>
          <div id="current-enrollments-container">
            <!-- Current enrollments will be loaded here -->
          </div>
        </div>
      </div>
    `;
  },

  // Initialize component
  async init() {
    await StudentManagementComponents.loadInitialData();
    this.populateDropdowns();
    this.populateEnrollmentSystemAdminFields();
    this.loadCurrentEnrollments();
    this.initializeEventListeners();
  },

  // Populate system admin fields for enrollment
  populateEnrollmentSystemAdminFields() {
    const currentAdminId = RegisterStudentComponent.getCurrentAdminId();

    if (currentAdminId) {
      const createdByField = document.getElementById('enrollment_created_by_id');
      const enrolledByField = document.getElementById('enrollment_enrolled_by_id');
      const updatedByField = document.getElementById('enrollment_updated_by_id');

      if (createdByField) createdByField.value = currentAdminId;
      if (enrolledByField) enrolledByField.value = currentAdminId;
      if (updatedByField) updatedByField.value = currentAdminId;
    }
  },

  // Populate dropdowns
  populateDropdowns() {
    // Populate students dropdown
    const studentSelect = document.getElementById('student_id');
    if (studentSelect) {
      studentSelect.innerHTML = '<option value="">Select Student</option>';
      const students = StudentManagementComponents.state.students.data || StudentManagementComponents.state.students;
      if (Array.isArray(students)) {
        students.forEach(student => {
          const option = document.createElement('option');
          option.value = student.id;
          option.textContent = `${student.first_name} ${student.last_name} (${student.admission_number})`;
          studentSelect.appendChild(option);
        });
      }
    }

    // Populate academic years
    const academicYearSelect = document.getElementById('academic_year_id');
    if (academicYearSelect) {
      academicYearSelect.innerHTML = '<option value="">Select Academic Year</option>';
      const academicYears = StudentManagementComponents.state.academicYears.data || StudentManagementComponents.state.academicYears;
      if (Array.isArray(academicYears)) {
        academicYears.forEach(year => {
          const option = document.createElement('option');
          option.value = year.id;
          option.textContent = year.name;
          if (year.is_active) option.selected = true;
          academicYearSelect.appendChild(option);
        });
      }
    }

    // Populate terms
    const termSelect = document.getElementById('term_id');
    if (termSelect) {
      termSelect.innerHTML = '<option value="">Select Term</option>';
      const terms = StudentManagementComponents.state.terms.data || StudentManagementComponents.state.terms;
      if (Array.isArray(terms)) {
        terms.forEach(term => {
          const option = document.createElement('option');
          option.value = term.id;
          option.textContent = term.name;
          if (term.is_active) option.selected = true;
          termSelect.appendChild(option);
        });
      }
    }

    // Populate classes
    const classSelect = document.getElementById('class_id');
    if (classSelect) {
      classSelect.innerHTML = '<option value="">Select Class</option>';
      const classes = StudentManagementComponents.state.classes.data || StudentManagementComponents.state.classes;
      if (Array.isArray(classes)) {
        classes.forEach(cls => {
          const option = document.createElement('option');
          option.value = cls.id;
          option.textContent = cls.name + (cls.stream_name ? ` - ${cls.stream_name}` : '');
          classSelect.appendChild(option);
        });
      }
    }

    // Populate A-Level combinations
    const combinationSelect = document.getElementById('combination_id');
    if (combinationSelect) {
      combinationSelect.innerHTML = '<option value="">Select Combination</option>';
      const combinations = StudentManagementComponents.state.combinations.data || StudentManagementComponents.state.combinations;
      if (Array.isArray(combinations)) {
        combinations.forEach(combination => {
          const option = document.createElement('option');
          option.value = combination.id;
          option.textContent = combination.name;
          combinationSelect.appendChild(option);
        });
      }
    }

    // Populate previous classes
    const previousClassSelect = document.getElementById('previous_class_id');
    if (previousClassSelect) {
      previousClassSelect.innerHTML = '<option value="">Select Previous Class (Optional)</option>';
      const classes = StudentManagementComponents.state.classes.data || StudentManagementComponents.state.classes;
      if (Array.isArray(classes)) {
        classes.forEach(cls => {
          const option = document.createElement('option');
          option.value = cls.id;
          option.textContent = cls.name + (cls.stream_name ? ` - ${cls.stream_name}` : '');
          previousClassSelect.appendChild(option);
        });
      }
    }
  },

  // Load current enrollments
  async loadCurrentEnrollments() {
    try {
      const container = document.getElementById('current-enrollments-container');
      if (!container) return;

      // Show loading state
      container.innerHTML = `
        <div class="p-6 text-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p class="text-gray-500">Loading current enrollments...</p>
        </div>
      `;

      // Get current enrollments from API
      const result = await window.StudentsAPI.getEnrollments();

      if (result.success) {
        const enrollments = result.data || result;
        this.renderCurrentEnrollments(Array.isArray(enrollments) ? enrollments : []);
      } else {
        container.innerHTML = `
          <div class="p-6 text-center text-red-500">
            <i class="fas fa-exclamation-triangle text-2xl mb-2"></i>
            <p>Failed to load enrollments: ${result.message || 'Unknown error'}</p>
          </div>
        `;
      }
    } catch (error) {
      console.error('Failed to load current enrollments:', error);
      const container = document.getElementById('current-enrollments-container');
      if (container) {
        container.innerHTML = `
          <div class="p-6 text-center text-red-500">
            <i class="fas fa-exclamation-triangle text-2xl mb-2"></i>
            <p>Failed to load enrollments</p>
          </div>
        `;
      }
    }
  },

  // Render current enrollments
  renderCurrentEnrollments(enrollments) {
    const container = document.getElementById('current-enrollments-container');
    if (!container) return;

    if (enrollments.length === 0) {
      container.innerHTML = `
        <div class="p-6 text-center text-gray-500">
          <i class="fas fa-user-check text-3xl mb-2"></i>
          <p>No current enrollments found</p>
        </div>
      `;
      return;
    }

    container.innerHTML = `
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Academic Year</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Term</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enrollment Date</th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            ${enrollments.map(enrollment => `
              <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">
                    ${enrollment.student_name || `${enrollment.first_name} ${enrollment.last_name}`}
                  </div>
                  <div class="text-sm text-gray-500">${enrollment.admission_number}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  ${enrollment.class_name}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  ${enrollment.academic_year_name}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  ${enrollment.term_name}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${this.getEnrollmentStatusClass(enrollment.status)}">
                    ${enrollment.status}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  ${enrollment.enrollment_date ? new Date(enrollment.enrollment_date).toLocaleDateString() : 'N/A'}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button onclick="StudentEnrollmentComponent.editEnrollment(${enrollment.id})"
                          class="text-indigo-600 hover:text-indigo-900 mr-3" title="Edit">
                    <i class="fas fa-edit"></i>
                  </button>
                  <button onclick="StudentEnrollmentComponent.deleteEnrollment(${enrollment.id})"
                          class="text-red-600 hover:text-red-900" title="Delete">
                    <i class="fas fa-trash"></i>
                  </button>
                </td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    `;
  },

  // Get enrollment status CSS class
  getEnrollmentStatusClass(status) {
    const classes = {
      'active': 'bg-green-100 text-green-800',
      'transferred': 'bg-blue-100 text-blue-800',
      'completed': 'bg-purple-100 text-purple-800',
      'dropped': 'bg-red-100 text-red-800',
      'pending': 'bg-yellow-100 text-yellow-800'
    };
    return classes[status] || 'bg-gray-100 text-gray-800';
  },

  // Initialize event listeners
  initializeEventListeners() {
    // Handle form submission
    const form = document.getElementById('enrollment-form');
    if (form) {
      form.addEventListener('submit', this.handleEnrollmentSubmit.bind(this));
    }
  },

  // Handle enrollment form submission
  async handleEnrollmentSubmit(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const data = Object.fromEntries(formData.entries());

    // Ensure system admin fields are populated
    const currentAdminId = RegisterStudentComponent.getCurrentAdminId();
    if (currentAdminId) {
      data.enrolled_by_id = currentAdminId;
      data.created_by_id = currentAdminId;
    }

    // Set enrollment date to current date
    data.enrollment_date = new Date().toISOString().split('T')[0];
    data.status = 'active';

    try {
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.forms) {
        window.AIMSDesignSystem.forms.setButtonLoading('enroll', true);
      }

      const result = await window.StudentsAPI.enroll(data);

      if (result.success) {
        // Show success notification
        if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
          window.AIMSDesignSystem.notifications.show('Student enrolled successfully!', 'success');
        }
        event.target.reset();
        // Reload enrollments
        await this.loadCurrentEnrollments();
      } else {
        // Show error notification
        if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
          window.AIMSDesignSystem.notifications.show(result.message || 'Failed to enroll student', 'error');
        }
      }
    } catch (error) {
      console.error('Enrollment error:', error);
      if (window.AIMSConfig && window.AIMSConfig.get('development.debugMode')) {
        console.error('Debug: Enrollment error details:', error);
      }
      // Show error notification
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
        window.AIMSDesignSystem.notifications.show('Failed to enroll student', 'error');
      }
    } finally {
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.forms) {
        window.AIMSDesignSystem.forms.setButtonLoading('enroll', false);
      }
    }
  },

  // Edit enrollment
  editEnrollment(enrollmentId) {
    // Implementation for editing enrollment
    console.log('Edit enrollment:', enrollmentId);
    // TODO: Implement enrollment editing functionality
  },

  // Delete enrollment
  async deleteEnrollment(enrollmentId) {
    if (!confirm('Are you sure you want to delete this enrollment? This action cannot be undone.')) {
      return;
    }

    try {
      const result = await window.StudentsAPI.deleteEnrollment(enrollmentId);

      if (result.success) {
        // Show success notification
        if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
          window.AIMSDesignSystem.notifications.show('Enrollment deleted successfully', 'success');
        }
        // Reload enrollments
        await this.loadCurrentEnrollments();
      } else {
        // Show error notification
        if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
          window.AIMSDesignSystem.notifications.show(result.message || 'Failed to delete enrollment', 'error');
        }
      }
    } catch (error) {
      console.error('Delete enrollment error:', error);
      if (window.AIMSConfig && window.AIMSConfig.get('development.debugMode')) {
        console.error('Debug: Delete enrollment error details:', error);
      }
      // Show error notification
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
        window.AIMSDesignSystem.notifications.show('Failed to delete enrollment', 'error');
      }
    }
  }
};

// Export components to global scope
window.RegisterStudentComponent = RegisterStudentComponent;
window.ManageStudentsComponent = ManageStudentsComponent;
window.StudentEnrollmentComponent = StudentEnrollmentComponent;
window.StudentManagementComponents = StudentManagementComponents;
