-- AIMS Database Schema
-- Academic Information Management System for Uganda Secondary Schools
-- Version: 1.0.0
-- Designed for competency-based assessment system
--
-- UGANDA ASSESSMENT SYSTEM:
--
-- O-LEVEL (UCE) ASSESSMENT:
-- Continuous Assessment (CA) - 20% Weight:
--   - Topic-based assessments
--   - Activities of Integration
--   - Projects
--   - Assignments
--   - Group work
--   - Practical exercises
-- End-of-Term Examinations - 80% Weight
-- Final Grade = CA (20%) + Exam (80%)
--
-- A-LEVEL (UACE) ASSESSMENT: (Principal Subjects)
-- Continuous Assessment (CA) - 20% Weight:
--   - Topic-based assessments
--   - Activities of Integration
--   - Projects
--   - Assignments
--   - Group work
--   - Practical exercises
-- End-of-Term Examinations - 80% Weight
-- Final Grade = CA (20%) + Exam (80%) 
-- Principal Subject Grades: A(6pts), B+(5pts), B(4pts), C+(3pts), C(2pts), D(1pt), E(0pts)

-- Subsidiary Subject Grades: D1-C6(1pt), P7-F9(0pts)
-- Total UACE Points = Best 3 Principal + Subsidiary subjects

-- Create database with proper character set
CREATE DATABASE IF NOT EXISTS aims_db 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE aims_db;

-- =============================================
-- 1. SYSTEM CONFIGURATION TABLES
-- =============================================

-- School settings table (stores all system configurations)
CREATE TABLE IF NOT EXISTS school_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT NULL,
    setting_type ENUM('string', 'integer', 'boolean', 'json') NOT NULL,
    category VARCHAR(50) NOT NULL,
    description TEXT NULL,
    is_editable BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_setting_key (setting_key),
    INDEX idx_category (category)
);

-- Competency levels table (0=Absent, 1=Basic, 2=Moderate, 3=Accomplished)
CREATE TABLE IF NOT EXISTS competency_levels (
    id INT PRIMARY KEY AUTO_INCREMENT,
    level_code TINYINT UNIQUE NOT NULL COMMENT '0-3 scale',
    level_name VARCHAR(50) NOT NULL COMMENT 'Absent, Basic, Moderate, Accomplished',
    level_descriptor TEXT NOT NULL,
    min_score DECIMAL(2,1) NOT NULL,
    max_score DECIMAL(2,1) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_level_code (level_code)
);

-- O-Level Grade Boundaries (Competency-Based Curriculum)
CREATE TABLE IF NOT EXISTS o_level_grade_boundaries (
    id INT PRIMARY KEY AUTO_INCREMENT,
    grade_letter VARCHAR(2) UNIQUE NOT NULL COMMENT 'A, B+, B, C+, C, D, E',
    min_percentage DECIMAL(5,2) NOT NULL,
    max_percentage DECIMAL(5,2) NOT NULL,
    grade_descriptor VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_o_level_grade_letter (grade_letter)
);

-- A-Level Principal Subjects Grade Boundaries (Competency-Based Curriculum)
CREATE TABLE IF NOT EXISTS a_level_principal_grade_boundaries (
    id INT PRIMARY KEY AUTO_INCREMENT,
    grade_letter VARCHAR(2) UNIQUE NOT NULL COMMENT 'A, B+, B, C+, C, D, E',
    min_percentage DECIMAL(5,2) NOT NULL,
    max_percentage DECIMAL(5,2) NOT NULL,
    grade_descriptor VARCHAR(50) NOT NULL,
    points INT NOT NULL DEFAULT 0 COMMENT 'Points for A-Level Principal grades',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_a_level_principal_grade_letter (grade_letter),
    INDEX idx_a_level_principal_points (points)
);

-- =============================================
-- 2. USER MANAGEMENT TABLES 
-- =============================================

-- System users table (for system administrators)
CREATE TABLE IF NOT EXISTS system_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    middle_name VARCHAR(50) NULL,
    role ENUM('system_admin') NOT NULL DEFAULT 'system_admin',
    phone_number VARCHAR(20) NULL,
    date_of_birth DATE NULL,
    gender ENUM('Male', 'Female') NULL,
    profile_picture VARCHAR(255) NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_active (is_active)
);

-- =============================================
-- 3. ACADEMIC STRUCTURE TABLES
-- =============================================

-- Academic years table
CREATE TABLE IF NOT EXISTS academic_years (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name ENUM('2025', '2026', '2027', '2028', '2029', '2030') UNIQUE NOT NULL COMMENT 'Academic year selection',
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT FALSE COMMENT 'Only one academic year can be active (current) at a time',
    created_by_id INT NOT NULL COMMENT 'System admin who created this academic year',
    updated_by_id INT NULL COMMENT 'System admin who last updated this academic year',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by_id) REFERENCES system_users(id) ON DELETE RESTRICT,
    FOREIGN KEY (updated_by_id) REFERENCES system_users(id) ON DELETE SET NULL,
    INDEX idx_active_year (is_active),
    INDEX idx_created_by (created_by_id),
    INDEX idx_updated_by (updated_by_id)
    -- Note: Date validation moved to business logic layer
);

-- Terms table
CREATE TABLE IF NOT EXISTS terms (
    id INT PRIMARY KEY AUTO_INCREMENT,
    academic_year_id INT NOT NULL,
    name VARCHAR(50) NOT NULL COMMENT 'e.g. Term 1',
    number TINYINT NOT NULL COMMENT '1, 2, or 3',
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT FALSE COMMENT 'Only one term can be active (current) at a time',
    created_by_id INT NOT NULL COMMENT 'System admin who created this term',
    updated_by_id INT NULL COMMENT 'System admin who last updated this term',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by_id) REFERENCES system_users(id) ON DELETE RESTRICT,
    FOREIGN KEY (updated_by_id) REFERENCES system_users(id) ON DELETE SET NULL,
    INDEX idx_academic_year (academic_year_id),
    INDEX idx_active_term (is_active),
    INDEX idx_created_by (created_by_id),
    INDEX idx_updated_by (updated_by_id),
    UNIQUE KEY uk_term_year (academic_year_id, number)
    -- Note: Date and term number validation moved to business logic layer
);

-- O-Level Subjects table (S1-S4)
CREATE TABLE IF NOT EXISTS o_level_subjects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    short_name VARCHAR(20) UNIQUE NOT NULL,
    subject_type ENUM(
        'Compulsory', 'Language',
        'Practical (pre-vocational)', 'Religious Education'
    ) NOT NULL,
    uneb_code VARCHAR(10) NOT NULL COMMENT 'Official UNEB subject codes (e.g., 112, 456)',
    exam_papers INT DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    created_by_id INT NOT NULL COMMENT 'System admin who created this subject',
    updated_by_id INT NULL COMMENT 'System admin who last updated this subject',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by_id) REFERENCES system_users(id) ON DELETE RESTRICT,
    FOREIGN KEY (updated_by_id) REFERENCES system_users(id) ON DELETE SET NULL,
    INDEX idx_subject_type (subject_type),
    INDEX idx_active (is_active),
    INDEX idx_uneb_code (uneb_code),
    INDEX idx_created_by (created_by_id),
    INDEX idx_updated_by (updated_by_id)
);

-- A-Level Subjects table (S5-S6)
CREATE TABLE IF NOT EXISTS a_level_subjects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    short_name VARCHAR(20) UNIQUE NOT NULL,
    subject_type ENUM('Principal', 'Subsidiary') NOT NULL,
    uneb_code VARCHAR(10) NOT NULL COMMENT 'Official UNEB subject codes (e.g., P425, P510, S101)',
    exam_papers INT DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    created_by_id INT NOT NULL COMMENT 'System admin who created this subject',
    updated_by_id INT NULL COMMENT 'System admin who last updated this subject',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by_id) REFERENCES system_users(id) ON DELETE RESTRICT,
    FOREIGN KEY (updated_by_id) REFERENCES system_users(id) ON DELETE SET NULL,
    INDEX idx_subject_type (subject_type),
    INDEX idx_active (is_active),
    INDEX idx_uneb_code (uneb_code),
    INDEX idx_created_by (created_by_id),
    INDEX idx_updated_by (updated_by_id)
);

-- =============================================
-- 3.5. LEVELS TABLE 
-- =============================================

-- Education Levels table (O-Level and A-Level) - the main education categories
CREATE TABLE IF NOT EXISTS education_levels (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(10) UNIQUE NOT NULL COMMENT 'o_level, a_level',
    name VARCHAR(50) NOT NULL COMMENT 'O Level, A Level',
    display_name VARCHAR(100) NOT NULL COMMENT 'Ordinary Level, Advanced Level',
    sort_order INT NOT NULL COMMENT 'For ordering levels (1-2)',
    is_active BOOLEAN DEFAULT TRUE,
    created_by_id INT NOT NULL COMMENT 'System admin who created this education level',
    updated_by_id INT NULL COMMENT 'System admin who last updated this education level',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by_id) REFERENCES system_users(id) ON DELETE RESTRICT,
    FOREIGN KEY (updated_by_id) REFERENCES system_users(id) ON DELETE SET NULL,
    INDEX idx_sort_order (sort_order),
    INDEX idx_active (is_active),
    INDEX idx_created_by (created_by_id),
    INDEX idx_updated_by (updated_by_id)
);

-- Classes table (S.1, S.2, S.3, S.4, S.5, S.6) - individual year groups within education levels
CREATE TABLE IF NOT EXISTS class_levels (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(10) UNIQUE NOT NULL COMMENT 'e.g. s1, s2, s3, s4, s5, s6',
    name VARCHAR(50) NOT NULL COMMENT 'e.g. S.1, S.2, S.3, S.4, S.5, S.6',
    display_name VARCHAR(100) NOT NULL COMMENT 'e.g. Senior One, Senior Two, etc.',
    education_level_id INT NOT NULL COMMENT 'Reference to education_levels table',
    sort_order INT NOT NULL COMMENT 'For ordering classes (1-6)',
    streams_optional BOOLEAN DEFAULT TRUE COMMENT 'Whether streams are optional for this class',
    is_active BOOLEAN DEFAULT TRUE,
    created_by_id INT NOT NULL COMMENT 'System admin who created this class level',
    updated_by_id INT NULL COMMENT 'System admin who last updated this class level',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (education_level_id) REFERENCES education_levels(id) ON DELETE RESTRICT,
    FOREIGN KEY (created_by_id) REFERENCES system_users(id) ON DELETE RESTRICT,
    FOREIGN KEY (updated_by_id) REFERENCES system_users(id) ON DELETE SET NULL,
    INDEX idx_education_level (education_level_id),
    INDEX idx_sort_order (sort_order),
    INDEX idx_active (is_active),
    INDEX idx_created_by (created_by_id),
    INDEX idx_updated_by (updated_by_id)
);

-- O-Level Subject-Class relationships (which classes each subject applies to)
CREATE TABLE IF NOT EXISTS o_level_subject_classes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    subject_id INT NOT NULL,
    class_level_id INT NOT NULL,
    is_compulsory BOOLEAN DEFAULT FALSE COMMENT 'Whether subject is compulsory at this class',
    is_elective BOOLEAN DEFAULT TRUE COMMENT 'Whether subject is available as elective at this class',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (subject_id) REFERENCES o_level_subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (class_level_id) REFERENCES class_levels(id) ON DELETE CASCADE,
    UNIQUE KEY uk_subject_class (subject_id, class_level_id),
    INDEX idx_subject (subject_id),
    INDEX idx_class_level (class_level_id),
    INDEX idx_compulsory (is_compulsory),
    INDEX idx_elective (is_elective)
);

-- A-Level Subject-Class relationships (which classes each subject applies to)
CREATE TABLE IF NOT EXISTS a_level_subject_classes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    subject_id INT NOT NULL,
    class_level_id INT NOT NULL,
    is_compulsory BOOLEAN DEFAULT FALSE COMMENT 'Whether subject is compulsory at this class',
    is_elective BOOLEAN DEFAULT TRUE COMMENT 'Whether subject is available as elective at this class',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (subject_id) REFERENCES a_level_subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (class_level_id) REFERENCES class_levels(id) ON DELETE CASCADE,
    UNIQUE KEY uk_subject_class (subject_id, class_level_id),
    INDEX idx_subject (subject_id),
    INDEX idx_class_level (class_level_id),
    INDEX idx_compulsory (is_compulsory),
    INDEX idx_elective (is_elective)
);

-- O-Level subject selection rules
CREATE TABLE IF NOT EXISTS o_level_selection_rules (
    id INT PRIMARY KEY AUTO_INCREMENT,
    class_level_id INT NOT NULL,
    total_subjects_required TINYINT NOT NULL,
    compulsory_subjects_count TINYINT NOT NULL,
    elective_subjects_count TINYINT NOT NULL,
    religious_education_required BOOLEAN DEFAULT TRUE,
    description TEXT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (class_level_id) REFERENCES class_levels(id) ON DELETE CASCADE,
    UNIQUE KEY uk_class_rules (class_level_id),
    INDEX idx_class_level (class_level_id),
    INDEX idx_active (is_active)
    -- Note: Subject count validation moved to business logic layer
);

-- A-Level subject selection rules
CREATE TABLE IF NOT EXISTS a_level_selection_rules (
    id INT PRIMARY KEY AUTO_INCREMENT,
    class_level_id INT NOT NULL,
    total_subjects_required TINYINT NOT NULL DEFAULT 5,
    compulsory_subjects_count TINYINT NOT NULL DEFAULT 1,
    subsidiary_subjects_count TINYINT NOT NULL DEFAULT 1,
    principal_subjects_count TINYINT NOT NULL DEFAULT 3,
    description TEXT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (class_level_id) REFERENCES class_levels(id) ON DELETE CASCADE,
    UNIQUE KEY uk_alevel_rules (class_level_id),
    INDEX idx_class_level (class_level_id),
    INDEX idx_active (is_active)
    -- Note: A-Level subject count validation moved to business logic layer
);

-- A-Level combination rules
CREATE TABLE IF NOT EXISTS a_level_combination_rules (
    id INT PRIMARY KEY AUTO_INCREMENT,
    combination_name VARCHAR(100) NOT NULL,
    combination_code VARCHAR(10) NOT NULL UNIQUE,
    combination_type ENUM('sciences', 'arts', 'mixed') NOT NULL,
    required_subsidiary ENUM('SMATH', 'SICT', 'either') NOT NULL DEFAULT 'either',
    principal_subject_1_id INT NOT NULL,
    principal_subject_2_id INT NOT NULL,
    principal_subject_3_id INT NOT NULL,
    stream_classification ENUM('sciences', 'arts') NOT NULL,
    is_popular BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (principal_subject_1_id) REFERENCES a_level_subjects(id) ON DELETE RESTRICT,
    FOREIGN KEY (principal_subject_2_id) REFERENCES a_level_subjects(id) ON DELETE RESTRICT,
    FOREIGN KEY (principal_subject_3_id) REFERENCES a_level_subjects(id) ON DELETE RESTRICT,
    INDEX idx_combination_type (combination_type),
    INDEX idx_stream_classification (stream_classification),
    INDEX idx_popular (is_popular),
    INDEX idx_active (is_active)
);



-- =============================================
-- 4. TEACHER MANAGEMENT 
-- =============================================

-- Teachers table
CREATE TABLE IF NOT EXISTS teachers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    middle_name VARCHAR(50) NULL,
    initials VARCHAR(10) NULL COMMENT 'Teacher initials (e.g., M.N.)',
    teaching_subjects TEXT NULL COMMENT 'JSON array of subject IDs',
    teacher_type ENUM('Class Teacher', 'Subject Teacher') DEFAULT 'Subject Teacher',
    joining_date DATE NULL,
    employment_status ENUM('active', 'inactive') DEFAULT 'active',
    profile_picture VARCHAR(255) NULL,
    academic_year_id INT NOT NULL COMMENT 'Academic year when teacher was registered',
    created_by_id INT NOT NULL DEFAULT 1 COMMENT 'System admin who created this teacher record',
    updated_by_id INT NULL COMMENT 'System admin who last updated this teacher record',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE RESTRICT,
    FOREIGN KEY (created_by_id) REFERENCES system_users(id) ON DELETE RESTRICT,
    FOREIGN KEY (updated_by_id) REFERENCES system_users(id) ON DELETE SET NULL,
    INDEX idx_employment_status (employment_status),
    INDEX idx_teacher_type (teacher_type),
    INDEX idx_initials (initials),
    INDEX idx_academic_year (academic_year_id),
    INDEX idx_created_by (created_by_id),
    INDEX idx_updated_by (updated_by_id)
);


-- =============================================
-- 5. CLASS AND STUDENT MANAGEMENT
-- =============================================


-- A-Level combinations table
CREATE TABLE IF NOT EXISTS a_level_combinations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    short_name VARCHAR(20) UNIQUE NOT NULL,
    combination_type ENUM('sciences', 'arts', 'mixed') NOT NULL,
    subsidiary_subject_id INT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_by_id INT NOT NULL DEFAULT 1 COMMENT 'System admin who created this combination',
    updated_by_id INT NULL COMMENT 'System admin who last updated this combination',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (subsidiary_subject_id) REFERENCES a_level_subjects(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by_id) REFERENCES system_users(id) ON DELETE RESTRICT,
    FOREIGN KEY (updated_by_id) REFERENCES system_users(id) ON DELETE SET NULL,
    INDEX idx_combination_type (combination_type),
    INDEX idx_active (is_active),
    INDEX idx_created_by (created_by_id),
    INDEX idx_updated_by (updated_by_id)
);

-- A-Level combination subjects
CREATE TABLE IF NOT EXISTS a_level_combination_subjects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    combination_id INT NOT NULL,
    subject_id INT NOT NULL,
    subject_type ENUM('Principal', 'Subsidiary') NOT NULL,
    is_required BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (combination_id) REFERENCES a_level_combinations(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES a_level_subjects(id) ON DELETE CASCADE,
    INDEX idx_combination (combination_id),
    INDEX idx_subject (subject_id),
    INDEX idx_subject_type (subject_type),
    UNIQUE KEY uk_combination_subject (combination_id, subject_id)
);

-- Streams table for managing class stream divisions (permanent structure)
CREATE TABLE IF NOT EXISTS streams (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL COMMENT 'e.g. A, B, C, D (O-Level), Sciences, Arts (A-Level)',
    stream_type ENUM('o_level', 'a_level') NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_by_id INT NOT NULL COMMENT 'System admin who created this stream',
    updated_by_id INT NULL COMMENT 'System admin who last updated this stream',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by_id) REFERENCES system_users(id) ON DELETE RESTRICT,
    FOREIGN KEY (updated_by_id) REFERENCES system_users(id) ON DELETE SET NULL,
    INDEX idx_stream_type (stream_type),
    INDEX idx_active (is_active),
    INDEX idx_created_by (created_by_id),
    INDEX idx_updated_by (updated_by_id),
    UNIQUE KEY uk_stream_type_name (name, stream_type)
);

-- Stream-Class relationships (which classes a stream applies to)
CREATE TABLE IF NOT EXISTS stream_classes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    stream_id INT NOT NULL,
    class_level_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (stream_id) REFERENCES streams(id) ON DELETE CASCADE,
    FOREIGN KEY (class_level_id) REFERENCES class_levels(id) ON DELETE CASCADE,
    UNIQUE KEY uk_stream_class (stream_id, class_level_id),
    INDEX idx_stream (stream_id),
    INDEX idx_class_level (class_level_id)
);

-- Classes table (permanent structure with stream divisions)
CREATE TABLE IF NOT EXISTS classes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT 'e.g. S.1, S.2 , S.3, S.5, S.6',
    class_level_id INT NOT NULL COMMENT 'Reference to class_levels table',
    stream_id INT NULL COMMENT 'Reference to streams table - NULL for base classes, NOT NULL for stream divisions',
    current_enrollment TINYINT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_by_id INT NOT NULL COMMENT 'System admin who created this class',
    updated_by_id INT NULL COMMENT 'System admin who last updated this class',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (class_level_id) REFERENCES class_levels(id) ON DELETE RESTRICT,
    FOREIGN KEY (stream_id) REFERENCES streams(id) ON DELETE RESTRICT,
    FOREIGN KEY (created_by_id) REFERENCES system_users(id) ON DELETE RESTRICT,
    FOREIGN KEY (updated_by_id) REFERENCES system_users(id) ON DELETE SET NULL,
    INDEX idx_class_level (class_level_id),
    INDEX idx_stream (stream_id),
    INDEX idx_active (is_active),
    INDEX idx_created_by (created_by_id),
    INDEX idx_updated_by (updated_by_id),
    UNIQUE KEY uk_class_level_stream (class_level_id, stream_id),
    UNIQUE KEY uk_class_name (name)
    -- Note: Stream divisions within class levels:
    -- O-Level: S.1, S.2, S.3, S.4 (base) + optional S.1 A, S.1 B, S.2 A, S.2 B, etc. (stream divisions)
    -- A-Level: S.5 Sciences, S.5 Arts, S.6 Sciences, S.6 Arts (mandatory stream divisions based on combinations)
);

-- Class subject assignments
CREATE TABLE IF NOT EXISTS class_subject_assignments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    class_id INT NOT NULL,
    subject_id INT NOT NULL,
    subject_level ENUM('o_level', 'a_level') NOT NULL,
    teacher_id INT NULL,
    periods_per_week TINYINT DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    created_by_id INT NOT NULL DEFAULT 1 COMMENT 'System admin who created this assignment',
    updated_by_id INT NULL COMMENT 'System admin who last updated this assignment',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by_id) REFERENCES system_users(id) ON DELETE RESTRICT,
    FOREIGN KEY (updated_by_id) REFERENCES system_users(id) ON DELETE SET NULL,
    INDEX idx_class (class_id),
    INDEX idx_subject_level (subject_id, subject_level),
    INDEX idx_active (is_active),
    INDEX idx_created_by (created_by_id),
    INDEX idx_updated_by (updated_by_id),
    UNIQUE KEY uk_class_subject (class_id, subject_id, subject_level)
    -- Note: Subject reference validation moved to business logic layer
);

-- Students table
CREATE TABLE IF NOT EXISTS students (
    id INT PRIMARY KEY AUTO_INCREMENT,
    admission_number VARCHAR(50) UNIQUE NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    middle_name VARCHAR(100) NULL,
    last_name VARCHAR(100) NOT NULL,
    gender ENUM('Male', 'Female') NOT NULL,
    status ENUM('active', 'transferred', 'graduated', 'dropped', 'suspended') DEFAULT 'active',
    passport_photo VARCHAR(255) NULL,
    created_by_id INT NOT NULL DEFAULT 1 COMMENT 'System admin who created this student record',
    updated_by_id INT NULL COMMENT 'System admin who last updated this student record',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by_id) REFERENCES system_users(id) ON DELETE RESTRICT,
    FOREIGN KEY (updated_by_id) REFERENCES system_users(id) ON DELETE SET NULL,
    INDEX idx_admission_number (admission_number),
    INDEX idx_names (first_name, last_name),
    INDEX idx_status (status),
    INDEX idx_created_by (created_by_id),
    INDEX idx_updated_by (updated_by_id)
);

-- Student subject selections
CREATE TABLE IF NOT EXISTS student_subject_selections (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    subject_id INT NOT NULL,
    subject_level ENUM('o_level', 'a_level') NOT NULL,
    selection_type ENUM('compulsory', 'elective') NOT NULL,
    class_level_id INT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    selection_date DATE NOT NULL,
    created_by_id INT NOT NULL DEFAULT 1 COMMENT 'System admin who created this selection',
    updated_by_id INT NULL COMMENT 'System admin who last updated this selection',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (class_level_id) REFERENCES class_levels(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by_id) REFERENCES system_users(id) ON DELETE RESTRICT,
    FOREIGN KEY (updated_by_id) REFERENCES system_users(id) ON DELETE SET NULL,
    INDEX idx_student (student_id),
    INDEX idx_subject_level (subject_id, subject_level),
    INDEX idx_class_level (class_level_id),
    INDEX idx_created_by (created_by_id),
    INDEX idx_updated_by (updated_by_id),
    UNIQUE KEY uk_student_subject_class (student_id, subject_id, subject_level, class_level_id)
    -- Note: Student subject reference validation moved to business logic layer
);

-- =============================================
-- 6. ASSESSMENT AND GRADING TABLES
-- =============================================

-- Assessments table (general assessment definitions)
CREATE TABLE IF NOT EXISTS assessments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    class_id INT NOT NULL,
    subject_id INT NOT NULL,
    subject_level ENUM('o_level', 'a_level') NOT NULL,
    teacher_id INT NOT NULL,
    academic_year_id INT NOT NULL,
    term_id INT NOT NULL,
    assessment_type ENUM('ca', 'exam') NOT NULL COMMENT 'CA (20%) or End-of-term Exam (80%)',
    ca_method ENUM('topic_assessment', 'activity_integration', 'project', 'assignment', 'group_work', 'practical_exercise') NULL COMMENT 'CA method if assessment_type is ca',
    weight_percentage DECIMAL(5,2) DEFAULT 100.00,
    assessment_date DATE NOT NULL,
    due_date DATE NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_by_id INT NOT NULL DEFAULT 1 COMMENT 'System admin who created this assessment',
    updated_by_id INT NULL COMMENT 'System admin who last updated this assessment',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by_id) REFERENCES system_users(id) ON DELETE RESTRICT,
    FOREIGN KEY (updated_by_id) REFERENCES system_users(id) ON DELETE SET NULL,
    INDEX idx_class (class_id),
    INDEX idx_subject_level (subject_id, subject_level),
    INDEX idx_term (term_id),
    INDEX idx_assessment_type (assessment_type),
    INDEX idx_created_by (created_by_id),
    INDEX idx_updated_by (updated_by_id)
    -- Note: Assessment subject reference validation moved to business logic layer
);

-- O-Level Continuous Assessments (CA - 20% weight)
CREATE TABLE IF NOT EXISTS o_level_continuous_assessments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    subject_id INT NOT NULL,
    academic_year_id INT NOT NULL,
    term_id INT NOT NULL,
    -- Competency Assessment
    competency_score DECIMAL(2,1) NOT NULL COMMENT 'Score field (1-3 scale for competency-based assessment)',
    competency_remark TEXT NULL COMMENT 'Competency Remark field - typed assessment description',

    -- Assessment Details
    assessment_date DATE NOT NULL,
    assessment_method ENUM('topic_assessment', 'activity_integration', 'project', 'assignment', 'group_work', 'practical_exercise') NOT NULL COMMENT 'CA methods as per Uganda curriculum',
    teacher_id INT NOT NULL,

    -- Additional Assessment Fields
    general_skills TEXT NULL COMMENT 'General Skills field - skills demonstrated',
    general_remarks TEXT NULL COMMENT 'General Remarks field - overall assessment remarks',
    created_by_id INT NOT NULL DEFAULT 1 COMMENT 'System admin who created this assessment',
    updated_by_id INT NULL COMMENT 'System admin who last updated this assessment',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES o_level_subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by_id) REFERENCES system_users(id) ON DELETE RESTRICT,
    FOREIGN KEY (updated_by_id) REFERENCES system_users(id) ON DELETE SET NULL,
    INDEX idx_student (student_id),
    INDEX idx_subject (subject_id),
    INDEX idx_academic_year (academic_year_id),
    INDEX idx_term (term_id),
    INDEX idx_assessment_method (assessment_method),
    INDEX idx_created_by (created_by_id),
    INDEX idx_updated_by (updated_by_id),
    UNIQUE KEY uk_student_subject_term (student_id, subject_id, term_id)
    -- Note: Competency score validation moved to business logic layer
);

-- A-Level Principal Subjects Continuous Assessments (CA - 20% weight)
CREATE TABLE IF NOT EXISTS a_level_principal_continuous_assessments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    subject_id INT NOT NULL,
    academic_year_id INT NOT NULL,
    term_id INT NOT NULL,
    -- Competency Assessment
    competency_score DECIMAL(2,1) NOT NULL COMMENT 'Score field (1-3 scale for competency-based assessment)',
    competency_remark TEXT NULL COMMENT 'Competency Remark field - typed assessment description',

    -- Assessment Details
    assessment_date DATE NOT NULL,
    assessment_method ENUM('topic_assessment', 'activity_integration', 'project', 'assignment', 'group_work', 'practical_exercise') NOT NULL COMMENT 'CA methods as per Uganda curriculum',
    teacher_id INT NOT NULL,

    -- Additional Assessment Fields
    general_skills TEXT NULL COMMENT 'General Skills field - skills demonstrated',
    general_remarks TEXT NULL COMMENT 'General Remarks field - overall assessment remarks',
    created_by_id INT NOT NULL DEFAULT 1 COMMENT 'System admin who created this assessment',
    updated_by_id INT NULL COMMENT 'System admin who last updated this assessment',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES a_level_subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by_id) REFERENCES system_users(id) ON DELETE RESTRICT,
    FOREIGN KEY (updated_by_id) REFERENCES system_users(id) ON DELETE SET NULL,
    INDEX idx_student (student_id),
    INDEX idx_subject (subject_id),
    INDEX idx_academic_year (academic_year_id),
    INDEX idx_term (term_id),
    INDEX idx_assessment_method (assessment_method),
    INDEX idx_created_by (created_by_id),
    INDEX idx_updated_by (updated_by_id),
    UNIQUE KEY uk_student_subject_term (student_id, subject_id, term_id)
    -- Note: Competency score and principal subject validation moved to business logic layer
);

-- O-Level Term Examinations (80% weight)
CREATE TABLE IF NOT EXISTS o_level_term_examinations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    class_id INT NOT NULL,
    subject_id INT NOT NULL,
    teacher_id INT NOT NULL,
    academic_year_id INT NOT NULL,
    term_id INT NOT NULL,
    max_marks INT NOT NULL DEFAULT 100,
    created_by_id INT NOT NULL DEFAULT 1 COMMENT 'System admin who created this examination',
    updated_by_id INT NULL COMMENT 'System admin who last updated this examination',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES o_level_subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by_id) REFERENCES system_users(id) ON DELETE RESTRICT,
    FOREIGN KEY (updated_by_id) REFERENCES system_users(id) ON DELETE SET NULL,
    INDEX idx_class (class_id),
    INDEX idx_subject (subject_id),
    INDEX idx_term (term_id),
    INDEX idx_created_by (created_by_id),
    INDEX idx_updated_by (updated_by_id),
    UNIQUE KEY uk_class_subject_term (class_id, subject_id, term_id)
);

-- A-Level Principal Subjects Term Examinations (80% weight)
CREATE TABLE IF NOT EXISTS a_level_principal_term_examinations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    class_id INT NOT NULL,
    subject_id INT NOT NULL,
    teacher_id INT NOT NULL,
    academic_year_id INT NOT NULL,
    term_id INT NOT NULL,
    max_marks INT NOT NULL DEFAULT 100,
    created_by_id INT NOT NULL DEFAULT 1 COMMENT 'System admin who created this examination',
    updated_by_id INT NULL COMMENT 'System admin who last updated this examination',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES a_level_subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by_id) REFERENCES system_users(id) ON DELETE RESTRICT,
    FOREIGN KEY (updated_by_id) REFERENCES system_users(id) ON DELETE SET NULL,
    INDEX idx_class (class_id),
    INDEX idx_subject (subject_id),
    INDEX idx_term (term_id),
    INDEX idx_created_by (created_by_id),
    INDEX idx_updated_by (updated_by_id),
    UNIQUE KEY uk_class_subject_term (class_id, subject_id, term_id)
    -- Note: Principal subject validation moved to business logic layer
);

-- Student examination grades (for end-of-term exams)
CREATE TABLE IF NOT EXISTS student_examination_grades (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    examination_id INT NOT NULL,
    academic_year_id INT NOT NULL,
    term_id INT NOT NULL,
    marks_obtained INT NULL COMMENT 'Raw marks out of max_marks',
    percentage_score INT NULL COMMENT 'Percentage score (0-100%)',
    grade_letter VARCHAR(5) NULL,
    remarks TEXT NULL,
    is_absent BOOLEAN DEFAULT FALSE,
    is_excused BOOLEAN DEFAULT FALSE,
    created_by_id INT NOT NULL DEFAULT 1 COMMENT 'System admin who created this grade record',
    updated_by_id INT NULL COMMENT 'System admin who last updated this grade record',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by_id) REFERENCES system_users(id) ON DELETE RESTRICT,
    FOREIGN KEY (updated_by_id) REFERENCES system_users(id) ON DELETE SET NULL,
    INDEX idx_student (student_id),
    INDEX idx_examination (examination_id),
    INDEX idx_academic_year (academic_year_id),
    INDEX idx_term (term_id),
    INDEX idx_created_by (created_by_id),
    INDEX idx_updated_by (updated_by_id),
    UNIQUE KEY uk_student_examination (student_id, examination_id)
);

-- O-Level Competency Grades (CA 20% + Exam 80% = Final Grade)
CREATE TABLE IF NOT EXISTS o_level_competency_grades (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    subject_id INT NOT NULL,
    academic_year_id INT NOT NULL,
    term_id INT NOT NULL,

    -- Continuous Assessment (20% weight)
    total_ca_points INT NOT NULL COMMENT 'Sum of all CA competency scores',
    assessed_ca_count INT NOT NULL COMMENT 'Number of CA assessments completed',
    average_ca_score INT NOT NULL COMMENT 'Average CA score (0-3 scale)',
    ca_percentage INT NOT NULL COMMENT '(average_ca_score ÷ 3) × 20 = CA contribution to final grade',

    -- Term Examination (80% weight)
    term_exam_score INT NULL COMMENT 'Term exam percentage (0-100%) × 0.8 = Exam contribution to final grade',

    -- Final Grade Calculation: CA (20%) + Exam (80%)
    final_score INT NULL COMMENT 'ca_percentage + term_exam_score = Final percentage',
    final_grade VARCHAR(2) NULL COMMENT 'A, B+, B, C+, C, D, E - Auto-calculated based on final_score',
    grade_descriptor TEXT NULL COMMENT 'Grade description - Auto-populated based on final_score',

    -- Teacher Assessment
    teacher_comment TEXT NULL,
    strengths TEXT NULL,
    areas_for_improvement TEXT NULL,
    recommendations TEXT NULL,

    -- Administrative
    is_final BOOLEAN DEFAULT FALSE,
    teacher_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES o_level_subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
    INDEX idx_student (student_id),
    INDEX idx_subject (subject_id),
    INDEX idx_term (term_id),
    UNIQUE KEY uk_student_subject_term (student_id, subject_id, term_id)
);

-- A-Level Principal Subjects Competency Grades (CA 20% + Exam 80% = Final Grade)
CREATE TABLE IF NOT EXISTS a_level_principal_competency_grades (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    subject_id INT NOT NULL,
    academic_year_id INT NOT NULL,
    term_id INT NOT NULL,

    -- Continuous Assessment (20% weight)
    total_ca_points INT NOT NULL COMMENT 'Sum of all CA competency scores',
    assessed_ca_count INT NOT NULL COMMENT 'Number of CA assessments completed',
    average_ca_score INT NOT NULL COMMENT 'Average CA score (0-3 scale)',
    ca_percentage INT NOT NULL COMMENT '(average_ca_score ÷ 3) × 20 = CA contribution to final grade',

    -- Term Examination (80% weight)
    term_exam_score INT NULL COMMENT 'Term exam percentage (0-100%) × 0.8 = Exam contribution to final grade',

    -- Final Grade Calculation: CA (20%) + Exam (80%)
    final_score INT NULL COMMENT 'ca_percentage + term_exam_score = Final percentage',
    final_grade VARCHAR(2) NULL COMMENT 'A, B+, B, C+, C, D, or E based on final_score',
    grade_descriptor TEXT NULL COMMENT 'Grade description based on competency achievement',

    -- Teacher Assessment
    teacher_comment TEXT NULL,
    strengths TEXT NULL,
    areas_for_improvement TEXT NULL,
    recommendations TEXT NULL,

    -- Administrative
    is_final BOOLEAN DEFAULT FALSE,
    teacher_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES a_level_subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
    INDEX idx_student (student_id),
    INDEX idx_subject (subject_id),
    INDEX idx_term (term_id),
    UNIQUE KEY uk_student_subject_term (student_id, subject_id, term_id)
    -- Note: Principal grade validation moved to business logic layer
);

-- A-Level Subsidiary Subjects Examinations (Only exam assessment - Traditional 0-100 marks, D1-F9 grading)
CREATE TABLE IF NOT EXISTS a_level_subsidiary_examinations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    subject_id INT NOT NULL,
    academic_year_id INT NOT NULL,
    term_id INT NOT NULL,
    marks_obtained INT NOT NULL COMMENT 'Marks scored (0-100)',
    total_marks INT NOT NULL DEFAULT 100 COMMENT 'Total possible marks',
    percentage INT NOT NULL COMMENT 'Percentage score (marks_obtained/total_marks * 100)',
    final_grade VARCHAR(2) NULL COMMENT 'D1, D2, C3, C4, C5, C6, P7, P8, F9',
    grade_points INT NULL COMMENT 'Points based on marks: D1-C6=1, P7-F9=0 - Auto-calculated',
    grade_description VARCHAR(255) NULL COMMENT 'Grade description from grading scale - Auto-populated',
    examination_date DATE NOT NULL,
    teacher_id INT NOT NULL,
    teacher_comment TEXT NULL,
    is_final BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES a_level_subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
    INDEX idx_student (student_id),
    INDEX idx_subject (subject_id),
    INDEX idx_academic_year (academic_year_id),
    INDEX idx_term (term_id),
    INDEX idx_final_grade (final_grade),
    INDEX idx_grade_points (grade_points),
    UNIQUE KEY uk_student_subject_term (student_id, subject_id, term_id)
    -- Note: Subsidiary exam validation moved to business logic layer
);

-- A-Level UACE Points Calculation Table (Total Points = Best 3 Principal + 1 Subsidiary)
CREATE TABLE IF NOT EXISTS a_level_uace_points (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    academic_year_id INT NOT NULL,
    term_id INT NOT NULL,

    -- Principal Subjects (Best 3 count towards UACE)
    principal_subject_1_id INT NULL COMMENT 'Best principal subject',
    principal_subject_1_grade VARCHAR(2) NULL COMMENT 'A, B+, B, C+, C, D, E',
    principal_subject_1_points INT NULL COMMENT 'Points for best principal subject',

    principal_subject_2_id INT NULL COMMENT 'Second best principal subject',
    principal_subject_2_grade VARCHAR(2) NULL COMMENT 'A, B+, B, C+, C, D, E',
    principal_subject_2_points INT NULL COMMENT 'Points for second best principal subject',

    principal_subject_3_id INT NULL COMMENT 'Third best principal subject',
    principal_subject_3_grade VARCHAR(2) NULL COMMENT 'A, B+, B, C+, C, D, E',
    principal_subject_3_points INT NULL COMMENT 'Points for third best principal subject',

    total_principal_points INT NULL COMMENT 'Sum of best 3 principal subjects points',

    -- Subsidiary Subject (1 counts towards UACE)
    subsidiary_subject_id INT NULL COMMENT 'Subsidiary subject (GP, SMATH, or SICT)',
    subsidiary_grade VARCHAR(2) NULL COMMENT 'D1, D2, C3, C4, C5, C6, P7, P8, F9',
    subsidiary_points INT NULL COMMENT 'Points for subsidiary subject',

    -- Total UACE Points
    total_uace_points INT NULL COMMENT 'Total UACE Points = Best 3 Principal + Subsidiary',
    uace_performance_level ENUM('Excellent', 'Very Good', 'Good', 'Satisfactory', 'Needs Improvement') NULL,

    -- Administrative
    is_final BOOLEAN DEFAULT FALSE,
    calculated_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE,
    FOREIGN KEY (principal_subject_1_id) REFERENCES a_level_subjects(id) ON DELETE SET NULL,
    FOREIGN KEY (principal_subject_2_id) REFERENCES a_level_subjects(id) ON DELETE SET NULL,
    FOREIGN KEY (principal_subject_3_id) REFERENCES a_level_subjects(id) ON DELETE SET NULL,
    FOREIGN KEY (subsidiary_subject_id) REFERENCES a_level_subjects(id) ON DELETE SET NULL,
    INDEX idx_student (student_id),
    INDEX idx_academic_year (academic_year_id),
    INDEX idx_term (term_id),
    INDEX idx_total_uace_points (total_uace_points),
    INDEX idx_uace_performance (uace_performance_level),
    UNIQUE KEY uk_student_year_term (student_id, academic_year_id, term_id)
);

-- =============================================
-- A-LEVEL COMPETENCY-BASED ASSESSMENT
-- =============================================
-- A-Level now uses the same competency-based assessment as O-Level - unified competency assessment throughout
--
-- A-Level students use the same tables as O-Level:
-- - continuous_assessments table for CA marks
-- - competency_grades table for final grades
-- - Same competency levels (0=Absent, 1=Basic, 2=Moderate, 3=Accomplished)
-- - Same grade boundaries (A=80-100%, B=70-79%, C=60-69%, D=50-59%, E=0-49%)
--
-- Benefits of unified system:
-- - Consistent assessment methodology across all levels
-- - Focus on competency development rather than traditional grades
-- - Easier transition from O-Level to A-Level assessment



-- =============================================
-- 6. REPORTING AND ANALYTICS
-- =============================================

-- Student report cards
CREATE TABLE IF NOT EXISTS student_report_cards (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    academic_year_id INT NOT NULL,
    term_id INT NOT NULL,
    report_type ENUM('end_of_term') NOT NULL DEFAULT 'end_of_term',
    class_id INT NOT NULL,
    level ENUM('o_level', 'a_level') NOT NULL COMMENT 'Report format type',
    grading_system ENUM('competency', 'traditional') NOT NULL,

    -- O-Level specific fields
    subjects_done INT NULL COMMENT 'Total number of subjects',
    average_points_score INT NULL COMMENT 'Overall CA average',
    identifier TINYINT NULL COMMENT 'Performance level indicator',
    overall_learner_achievement ENUM('ACCOMPLISHED', 'MODERATE', 'BASIC', 'ELEMENTARY') NULL COMMENT 'O-Level achievement level',
    generic_skills TEXT NULL COMMENT 'Generic skills assessment',
    general_remarks TEXT NULL COMMENT 'General remarks',

    -- Common fields
    overall_grade VARCHAR(10) NULL,
    overall_final_score INT NULL,
    total_subjects INT NULL,
    total_ca_points INT NULL,
    total_assessed_cas INT NULL,
    ca_percentage INT NULL,
    exam_average INT NULL,


    -- Comments and signatures
    class_teacher_comment TEXT NULL,
    class_teacher_signature VARCHAR(255) NULL COMMENT 'Path to signature image or digital signature',
    head_teacher_comment TEXT NULL,
    head_teacher_signature VARCHAR(255) NULL COMMENT 'Path to signature image or digital signature',

    -- Term dates
    next_term_begins DATE NULL,
    term_ends DATE NULL,

    -- Administrative
    date_printed DATE NULL,
    generated_at TIMESTAMP NOT NULL,
    is_published BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
    INDEX idx_student (student_id),
    INDEX idx_term (term_id),
    INDEX idx_level (level),
    INDEX idx_report_type (report_type),
    UNIQUE KEY uk_student_term (student_id, term_id)
);

-- Report card subjects (detailed subject performance)
CREATE TABLE IF NOT EXISTS report_card_subjects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    report_card_id INT NOT NULL,
    subject_id INT NOT NULL,
    teacher_id INT NULL COMMENT 'Subject teacher reference',
    teacher_initials VARCHAR(10) NULL COMMENT 'Teacher initials for report card display',

    -- O-Level CA structure (C1-C9 columns)
    c1_score DECIMAL(2,1) NULL COMMENT 'CA score 1 (0-3)',
    c2_score DECIMAL(2,1) NULL COMMENT 'CA score 2 (0-3)',
    c3_score DECIMAL(2,1) NULL COMMENT 'CA score 3 (0-3)',
    c4_score DECIMAL(2,1) NULL COMMENT 'CA score 4 (0-3)',
    c5_score DECIMAL(2,1) NULL COMMENT 'CA score 5 (0-3)',
    c6_score DECIMAL(2,1) NULL COMMENT 'CA score 6 (0-3)',
    c7_score DECIMAL(2,1) NULL COMMENT 'CA score 7 (0-3)',
    c8_score DECIMAL(2,1) NULL COMMENT 'CA score 8 (0-3)',
    c9_score DECIMAL(2,1) NULL COMMENT 'CA score 9 (0-3)',

    -- O-Level calculations
    total_ca_points INT NULL COMMENT 'Sum of all CA scores',
    assessed_count INT NULL COMMENT 'Number of assessments completed',
    max_possible_points INT NULL COMMENT 'Maximum possible CA points',
    out_of_20 INT NULL COMMENT 'CA converted to 20-point scale',
    identifier TINYINT NULL COMMENT 'Grade level indicator (0-3)',

    -- A-Level structure (now using competency-based assessment)
    subject_type ENUM('compulsory', 'subsidiary', 'principal') NULL COMMENT 'A-Level subject classification',

    -- Common fields
    term_exam_score INT NULL,
    final_score INT NULL,
    final_grade VARCHAR(2) NULL,
    grade_descriptor VARCHAR(50) NULL,

    -- Teacher comments (A-Level has subject teacher comments)
    teacher_comment TEXT NULL COMMENT 'Subject teacher comment (especially for A-Level)',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (report_card_id) REFERENCES student_report_cards(id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE SET NULL,
    INDEX idx_report_card (report_card_id),
    INDEX idx_subject (subject_id),
    INDEX idx_teacher (teacher_id),
    UNIQUE KEY uk_report_subject (report_card_id, subject_id)
);

-- =============================================
-- 7. Other Tables
-- =============================================




-- Teacher subjects table for tracking teacher-subject assignments
CREATE TABLE IF NOT EXISTS teacher_subjects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    teacher_id INT NOT NULL,
    subject_id INT NOT NULL,
    subject_level ENUM('o_level', 'a_level') NOT NULL,
    assigned_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
    INDEX idx_teacher (teacher_id),
    INDEX idx_subject_level (subject_id, subject_level),
    INDEX idx_active (is_active),
    UNIQUE KEY uk_teacher_subject (teacher_id, subject_id, subject_level)
    -- Note: Teacher subject reference validation moved to business logic layer
);

-- O-Level student subjects table
CREATE TABLE IF NOT EXISTS o_level_student_subjects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    subject_id INT NOT NULL,
    selection_type ENUM('compulsory', 'elective') NOT NULL,
    class_level_id INT NOT NULL,
    selection_date DATE NOT NULL,
    can_change BOOLEAN DEFAULT TRUE COMMENT 'Whether student can change this subject selection',
    change_deadline DATE NULL COMMENT 'Deadline for changing this subject',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES o_level_subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (class_level_id) REFERENCES class_levels(id) ON DELETE CASCADE,
    INDEX idx_student (student_id),
    INDEX idx_subject (subject_id),
    INDEX idx_selection_type (selection_type),
    INDEX idx_class_level (class_level_id),
    INDEX idx_active (is_active),
    UNIQUE KEY uk_student_subject_class_level (student_id, subject_id, class_level_id)
);

-- A-Level student subjects table
CREATE TABLE IF NOT EXISTS a_level_student_subjects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    subject_id INT NOT NULL,
    class_level_id INT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES a_level_subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (class_level_id) REFERENCES class_levels(id) ON DELETE CASCADE,
    INDEX idx_student (student_id),
    INDEX idx_subject (subject_id),
    INDEX idx_class_level (class_level_id),
    INDEX idx_active (is_active),
    UNIQUE KEY uk_student_subject_class_level (student_id, subject_id, class_level_id)
);

-- =============================================
-- 8. ACADEMIC OPERATIONS TABLES
-- =============================================

-- Grade components table (for detailed assessment breakdown)
-- Continuous Assessment (CA) - 20% Weight:
--   - Topic-based assessments
--   - Activities of Integration
--   - Projects
--   - Assignments
--   - Group work
--   - Practical exercises
CREATE TABLE IF NOT EXISTS grade_components (
    id INT PRIMARY KEY AUTO_INCREMENT,
    assessment_id INT NOT NULL,
    academic_year_id INT NOT NULL,
    term_id INT NOT NULL,
    component_name ENUM('topic_assessment', 'activity_integration', 'project', 'assignment', 'group_work', 'practical_exercise') NOT NULL COMMENT 'CA assessment methods as per Uganda curriculum',
    weight_percentage DECIMAL(5,2) DEFAULT 100.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (assessment_id) REFERENCES assessments(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE,
    INDEX idx_assessment (assessment_id),
    INDEX idx_academic_year (academic_year_id),
    INDEX idx_term (term_id),
    INDEX idx_component_name (component_name)
);









-- Student enrollments (System admin updates each term)
CREATE TABLE IF NOT EXISTS student_enrollments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    class_id INT NOT NULL,
    academic_year_id INT NOT NULL,
    term_id INT NOT NULL,
    combination_id INT NULL COMMENT 'For A-Level students only',
    enrollment_date DATE NOT NULL,
    enrolled_by_id INT NOT NULL COMMENT 'System admin who enrolled the student',
    status ENUM('active', 'transferred', 'completed', 'dropped', 'pending') DEFAULT 'active',
    enrollment_notes TEXT NULL COMMENT 'Notes about the enrollment',
    previous_class_id INT NULL COMMENT 'Previous class if transferred within term',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE,
    FOREIGN KEY (combination_id) REFERENCES a_level_combinations(id) ON DELETE SET NULL,
    FOREIGN KEY (enrolled_by_id) REFERENCES system_users(id) ON DELETE RESTRICT,
    FOREIGN KEY (previous_class_id) REFERENCES classes(id) ON DELETE SET NULL,
    UNIQUE KEY uk_student_term (student_id, term_id),
    INDEX idx_student (student_id),
    INDEX idx_class (class_id),
    INDEX idx_combination (combination_id),
    INDEX idx_enrolled_by (enrolled_by_id),
    INDEX idx_status (status)
);







-- O-Level student promotions table (S.1 → S.2 → S.3 → S.4)
CREATE TABLE IF NOT EXISTS o_level_student_promotions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    from_class_id INT NOT NULL,
    to_class_id INT NOT NULL,
    academic_year_id INT NOT NULL COMMENT 'Academic year when promotion occurred',
    promoted_by_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (from_class_id) REFERENCES classes(id) ON DELETE RESTRICT,
    FOREIGN KEY (to_class_id) REFERENCES classes(id) ON DELETE RESTRICT,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (promoted_by_id) REFERENCES system_users(id) ON DELETE RESTRICT,
    INDEX idx_student (student_id),
    INDEX idx_academic_year (academic_year_id),
    INDEX idx_from_class (from_class_id),
    INDEX idx_to_class (to_class_id)
);

-- A-Level student registrations table (initial entry into A-Level system)
CREATE TABLE IF NOT EXISTS a_level_student_registrations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL COMMENT 'Student record (same for internal students, new for external students)',
    registration_type ENUM('internal_graduate', 'external_transfer') DEFAULT 'internal_graduate' COMMENT 'Type of A-Level registration',
    previous_admission_number VARCHAR(50) NULL COMMENT 'Previous O-Level admission number (for internal graduates)',
    new_admission_number VARCHAR(50) NOT NULL COMMENT 'New unique A-Level admission number',
    class_id INT NOT NULL COMMENT 'Initial A-Level class (S.5 Sciences/Arts only)',
    academic_year_id INT NOT NULL COMMENT 'Academic year of A-Level registration',
    registered_by_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE RESTRICT,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (registered_by_id) REFERENCES system_users(id) ON DELETE RESTRICT,
    INDEX idx_student (student_id),
    INDEX idx_academic_year (academic_year_id),
    INDEX idx_class (class_id),
    INDEX idx_registration_type (registration_type),
    UNIQUE KEY uk_new_admission_number (new_admission_number)
);

-- A-Level student promotions table (S.5 → S.6 progression within A-Level system)
CREATE TABLE IF NOT EXISTS a_level_student_promotions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL COMMENT 'A-Level student (same admission number)',
    from_class_id INT NOT NULL COMMENT 'S.5 Sciences or S.5 Arts',
    to_class_id INT NOT NULL COMMENT 'S.6 Sciences or S.6 Arts',
    academic_year_id INT NOT NULL COMMENT 'Academic year when promotion occurred',
    promoted_by_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (from_class_id) REFERENCES classes(id) ON DELETE RESTRICT,
    FOREIGN KEY (to_class_id) REFERENCES classes(id) ON DELETE RESTRICT,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (promoted_by_id) REFERENCES system_users(id) ON DELETE RESTRICT,
    INDEX idx_student (student_id),
    INDEX idx_academic_year (academic_year_id),
    INDEX idx_from_class (from_class_id),
    INDEX idx_to_class (to_class_id)
);

-- Student status changes (transfers, dropouts, etc.)
CREATE TABLE IF NOT EXISTS student_status_changes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    from_status ENUM('active', 'transferred', 'completed', 'dropped', 'suspended') NOT NULL,
    to_status ENUM('active', 'transferred', 'completed', 'dropped', 'suspended') NOT NULL,
    change_date DATE NOT NULL,
    academic_year_id INT NOT NULL,
    term_id INT NOT NULL,
    reason TEXT NOT NULL COMMENT 'Reason for status change',
    transfer_school VARCHAR(200) NULL COMMENT 'School transferred to (if applicable)',
    transfer_class VARCHAR(50) NULL COMMENT 'Class transferred to (if applicable)',
    graduation_level ENUM('o_level', 'a_level') NULL COMMENT 'Level graduated from',
    dropout_reason TEXT NULL COMMENT 'Specific reason for dropping out',
    suspension_duration INT NULL COMMENT 'Suspension duration in days',
    suspension_end_date DATE NULL,
    effective_date DATE NOT NULL COMMENT 'When the status change takes effect',
    processed_by_id INT NOT NULL COMMENT 'System admin who processed the change',
    parent_notified BOOLEAN DEFAULT FALSE,
    parent_notification_date DATE NULL,
    documents_returned BOOLEAN DEFAULT FALSE COMMENT 'Student documents returned',
    fees_cleared BOOLEAN DEFAULT FALSE COMMENT 'School fees cleared',
    library_cleared BOOLEAN DEFAULT FALSE COMMENT 'Library books returned',
    final_clearance BOOLEAN DEFAULT FALSE COMMENT 'Final clearance completed',
    notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE,
    FOREIGN KEY (processed_by_id) REFERENCES system_users(id) ON DELETE RESTRICT,
    INDEX idx_student (student_id),
    INDEX idx_from_status (from_status),
    INDEX idx_to_status (to_status),
    INDEX idx_change_date (change_date),
    INDEX idx_effective_date (effective_date)
);

-- A-Level specific subject change rules
CREATE TABLE IF NOT EXISTS a_level_subject_change_rules (
    id INT PRIMARY KEY AUTO_INCREMENT,
    rule_name VARCHAR(255) NOT NULL,
    class_level_id INT NOT NULL,
    change_type ENUM('principal_subject', 'subsidiary_subject', 'combination') NOT NULL,
    academic_year_id INT NULL COMMENT 'Academic year these rules apply to (NULL for global rules)',
    description TEXT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_by_id INT NOT NULL DEFAULT 1 COMMENT 'System admin who created this rule',
    updated_by_id INT NULL COMMENT 'System admin who last updated this rule',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (class_level_id) REFERENCES class_levels(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by_id) REFERENCES system_users(id) ON DELETE RESTRICT,
    FOREIGN KEY (updated_by_id) REFERENCES system_users(id) ON DELETE SET NULL,
    INDEX idx_class_level (class_level_id),
    INDEX idx_change_type (change_type),
    INDEX idx_academic_year (academic_year_id),
    INDEX idx_active (is_active),
    INDEX idx_created_by (created_by_id),
    INDEX idx_updated_by (updated_by_id)
);

-- A-Level specific subject change rules
CREATE TABLE IF NOT EXISTS a_level_subject_change_rules (
    id INT PRIMARY KEY AUTO_INCREMENT,
    rule_name VARCHAR(255) NOT NULL,
    class_level_id INT NOT NULL,
    change_type ENUM('principal_subject', 'subsidiary_subject', 'combination') NOT NULL,
    academic_year_id INT NULL COMMENT 'Academic year these rules apply to (NULL for global rules)',
    description TEXT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_by_id INT NOT NULL DEFAULT 1 COMMENT 'System admin who created this rule',
    updated_by_id INT NULL COMMENT 'System admin who last updated this rule',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (class_level_id) REFERENCES class_levels(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by_id) REFERENCES system_users(id) ON DELETE RESTRICT,
    FOREIGN KEY (updated_by_id) REFERENCES system_users(id) ON DELETE SET NULL,
    INDEX idx_class_level (class_level_id),
    INDEX idx_change_type (change_type),
    INDEX idx_academic_year (academic_year_id),
    INDEX idx_active (is_active),
    INDEX idx_created_by (created_by_id),
    INDEX idx_updated_by (updated_by_id)
);



-- =============================================
-- 10. GRADING SCALES FOR COMPETENCY-BASED ASSESSMENT
-- =============================================

-- O-Level Competency-Based Grading Scale
CREATE TABLE IF NOT EXISTS o_level_grading_scale (
    id INT PRIMARY KEY AUTO_INCREMENT,
    competency_level INT NOT NULL COMMENT 'Competency level (0, 1, 2, 3)',
    competency_description VARCHAR(255) NOT NULL COMMENT 'Description of competency level',
    min_score DECIMAL(3,2) NOT NULL COMMENT 'Minimum score for this level (0.00-3.00)',
    max_score DECIMAL(3,2) NOT NULL COMMENT 'Maximum score for this level (0.00-3.00)',
    grade_comment TEXT NULL COMMENT 'Detailed comment about this grade level',
    is_pass BOOLEAN DEFAULT TRUE COMMENT 'Whether this grade is considered a pass',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'Whether this grade level is currently active',
    created_by_id INT NOT NULL DEFAULT 1 COMMENT 'System admin who created this grading scale',
    updated_by_id INT NULL COMMENT 'System admin who last updated this grading scale',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by_id) REFERENCES system_users(id) ON DELETE RESTRICT,
    FOREIGN KEY (updated_by_id) REFERENCES system_users(id) ON DELETE SET NULL,
    INDEX idx_competency_level (competency_level),
    INDEX idx_active (is_active),
    INDEX idx_created_by (created_by_id),
    INDEX idx_updated_by (updated_by_id),
    UNIQUE KEY uk_competency_level (competency_level),
    CONSTRAINT chk_score_range CHECK (min_score >= 0.00 AND max_score <= 3.00 AND min_score <= max_score)
);

-- A-Level Principal Subjects Competency-Based Grading Scale
CREATE TABLE IF NOT EXISTS a_level_principal_grading_scale (
    id INT PRIMARY KEY AUTO_INCREMENT,
    competency_level INT NOT NULL COMMENT 'Competency level (1, 2, 3, 4)',
    competency_description VARCHAR(255) NOT NULL,
    min_score DECIMAL(5,2) NOT NULL,
    max_score DECIMAL(5,2) NOT NULL,
    is_pass BOOLEAN DEFAULT TRUE,
    is_active BOOLEAN DEFAULT TRUE,
    created_by_id INT NOT NULL DEFAULT 1 COMMENT 'System admin who created this grading scale',
    updated_by_id INT NULL COMMENT 'System admin who last updated this grading scale',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by_id) REFERENCES system_users(id) ON DELETE RESTRICT,
    FOREIGN KEY (updated_by_id) REFERENCES system_users(id) ON DELETE SET NULL,
    INDEX idx_competency_level (competency_level),
    INDEX idx_active (is_active),
    INDEX idx_created_by (created_by_id),
    INDEX idx_updated_by (updated_by_id),
    UNIQUE KEY uk_competency_level (competency_level)
);

-- A-Level Subsidiary Subjects Traditional Grading Scale (D1-F9)
CREATE TABLE IF NOT EXISTS a_level_subsidiary_grading_scale (
    id INT PRIMARY KEY AUTO_INCREMENT,
    grade_code VARCHAR(10) NOT NULL COMMENT 'Grade codes: D1, D2, C3, C4, C5, C6, P7, P8, F9',
    grade_description VARCHAR(255) NOT NULL,
    min_score DECIMAL(5,2) NOT NULL,
    max_score DECIMAL(5,2) NOT NULL,
    points INT DEFAULT NULL,
    is_pass BOOLEAN DEFAULT TRUE,
    is_active BOOLEAN DEFAULT TRUE,
    created_by_id INT NOT NULL DEFAULT 1 COMMENT 'System admin who created this grading scale',
    updated_by_id INT NULL COMMENT 'System admin who last updated this grading scale',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by_id) REFERENCES system_users(id) ON DELETE RESTRICT,
    FOREIGN KEY (updated_by_id) REFERENCES system_users(id) ON DELETE SET NULL,
    INDEX idx_grade_code (grade_code),
    INDEX idx_active (is_active),
    INDEX idx_created_by (created_by_id),
    INDEX idx_updated_by (updated_by_id),
    UNIQUE KEY uk_grade_code (grade_code)
);

-- =============================================
-- 11. STORED PROCEDURES FOR STREAM ASSIGNMENT
-- =============================================

-- Function to determine A-Level stream based on student's subject combination
DELIMITER //
CREATE FUNCTION determine_a_level_stream(p_student_id INT, p_academic_year_id INT)
RETURNS VARCHAR(20)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_stream VARCHAR(20) DEFAULT 'Arts';
    DECLARE v_science_count INT DEFAULT 0;
    DECLARE v_has_principal_math BOOLEAN DEFAULT FALSE;

    -- Check if student has Principal Mathematics
    SELECT COUNT(*) INTO v_has_principal_math
    FROM a_level_student_subjects ass
    JOIN a_level_subjects sub ON ass.subject_id = sub.id
    WHERE ass.student_id = p_student_id
    AND sub.name = 'Principal Mathematics'
    AND sub.subject_type = 'Principal'
    AND ass.is_active = TRUE;

    -- Count science principal subjects (Physics, Chemistry, Biology, Agriculture)
    SELECT COUNT(*) INTO v_science_count
    FROM a_level_student_subjects ass
    JOIN a_level_subjects sub ON ass.subject_id = sub.id
    WHERE ass.student_id = p_student_id
    AND sub.subject_type = 'Principal'
    AND sub.name IN ('Physics', 'Chemistry', 'Biology', 'Agriculture')
    AND ass.is_active = TRUE;

    -- Determine stream: Sciences if has Principal Math OR any science subjects
    IF v_has_principal_math > 0 OR v_science_count > 0 THEN
        SET v_stream = 'Sciences';
    ELSE
        SET v_stream = 'Arts';
    END IF;

    RETURN v_stream;
END//
DELIMITER ;

-- Procedure to get the appropriate A-Level class for a student
DELIMITER //
CREATE PROCEDURE get_a_level_class_for_student(
    IN p_student_id INT,
    IN p_level_code VARCHAR(10),
    IN p_academic_year_id INT,
    OUT p_class_id INT,
    OUT p_class_name VARCHAR(100),
    OUT p_stream_name VARCHAR(50)
)
BEGIN
    DECLARE v_stream_name VARCHAR(20);
    DECLARE v_class_level_id INT;

    -- Get the class level ID
    SELECT id INTO v_class_level_id FROM class_levels WHERE code = p_level_code LIMIT 1;

    -- Determine the appropriate stream
    SET v_stream_name = determine_a_level_stream(p_student_id, p_academic_year_id);

    -- Get the class information
    SELECT c.id, c.name, s.name
    INTO p_class_id, p_class_name, p_stream_name
    FROM classes c
    JOIN streams s ON c.stream_id = s.id
    WHERE c.class_level_id = v_class_level_id
    AND s.name = v_stream_name
    AND s.stream_type = 'a_level'
    AND c.is_active = TRUE
    LIMIT 1;
END//
DELIMITER ;

-- =============================================
-- 12. TRIGGERS FOR AUTOMATIC CALCULATIONS
-- =============================================

-- Trigger to update student status when status change is recorded
DELIMITER //
CREATE TRIGGER update_student_status
AFTER INSERT ON student_status_changes
FOR EACH ROW
BEGIN
    -- Update the student's status in the main students table
    UPDATE students
    SET status = NEW.to_status,
        updated_at = NOW()
    WHERE id = NEW.student_id;

    -- If student is being transferred, dropped, or completed, deactivate current enrollments
    IF NEW.to_status IN ('transferred', 'dropped', 'completed') THEN
        UPDATE student_enrollments
        SET status = NEW.to_status,
            updated_at = NOW()
        WHERE student_id = NEW.student_id
        AND status = 'active';
    END IF;
END//
DELIMITER ;


-- Note: CA calculations are handled by the application layer for O-Level and A-Level separately
-- since they use different table structures (o_level_continuous_assessments, a_level_principal_continuous_assessments)

-- Note: Final score calculations are handled by the application layer for O-Level and A-Level separately
-- since they use different grading tables (o_level_grade_boundaries, a_level_principal_grade_boundaries)

-- =============================================
-- A-LEVEL COMPETENCY-BASED ASSESSMENT TRIGGERS
-- =============================================
-- A-Level now uses the same competency-based triggers as O-Level
-- No separate UACE calculation triggers needed


-- Trigger to populate teacher initials in report card subjects
DELIMITER //
CREATE TRIGGER populate_teacher_initials_report
BEFORE INSERT ON report_card_subjects
FOR EACH ROW
BEGIN
    DECLARE teacher_initials_val VARCHAR(10);

    -- Get teacher initials if teacher_id is provided
    IF NEW.teacher_id IS NOT NULL THEN
        SELECT initials INTO teacher_initials_val
        FROM teachers
        WHERE id = NEW.teacher_id;

        SET NEW.teacher_initials = teacher_initials_val;
    END IF;
END//
DELIMITER ;

-- Trigger to update teacher initials when teacher is changed
DELIMITER //
CREATE TRIGGER update_teacher_initials_report
BEFORE UPDATE ON report_card_subjects
FOR EACH ROW
BEGIN
    DECLARE teacher_initials_val VARCHAR(10);

    -- Update teacher initials if teacher_id has changed
    IF NEW.teacher_id != OLD.teacher_id OR (NEW.teacher_id IS NOT NULL AND OLD.teacher_id IS NULL) THEN
        IF NEW.teacher_id IS NOT NULL THEN
            SELECT initials INTO teacher_initials_val
            FROM teachers
            WHERE id = NEW.teacher_id;

            SET NEW.teacher_initials = teacher_initials_val;
        ELSE
            SET NEW.teacher_initials = NULL;
        END IF;
    END IF;
END//
DELIMITER ;

-- Trigger to automatically calculate UACE points when A-Level Principal grades are updated
DELIMITER //
CREATE TRIGGER calculate_uace_points_principal
AFTER INSERT ON a_level_principal_competency_grades
FOR EACH ROW
BEGIN
    CALL calculate_student_uace_points(NEW.student_id, NEW.academic_year_id, NEW.term_id);
END//
DELIMITER ;

-- Trigger to automatically update UACE points when A-Level Principal grades are updated
DELIMITER //
CREATE TRIGGER update_uace_points_principal
AFTER UPDATE ON a_level_principal_competency_grades
FOR EACH ROW
BEGIN
    CALL calculate_student_uace_points(NEW.student_id, NEW.academic_year_id, NEW.term_id);
END//
DELIMITER ;

-- Trigger to automatically calculate UACE points when A-Level Subsidiary grades are updated
DELIMITER //
CREATE TRIGGER calculate_uace_points_subsidiary
AFTER INSERT ON a_level_subsidiary_examinations
FOR EACH ROW
BEGIN
    CALL calculate_student_uace_points(NEW.student_id, NEW.academic_year_id, NEW.term_id);
END//
DELIMITER ;

-- Trigger to automatically update UACE points when A-Level Subsidiary grades are updated
DELIMITER //
CREATE TRIGGER update_uace_points_subsidiary
AFTER UPDATE ON a_level_subsidiary_examinations
FOR EACH ROW
BEGIN
    CALL calculate_student_uace_points(NEW.student_id, NEW.academic_year_id, NEW.term_id);
END//
DELIMITER ;

-- Trigger to automatically calculate grade and points for A-Level Subsidiary subjects based on percentage
DELIMITER //
CREATE TRIGGER calculate_subsidiary_grade_points
BEFORE INSERT ON a_level_subsidiary_examinations
FOR EACH ROW
BEGIN
    -- Calculate grade and points based on percentage
    IF NEW.percentage >= 80 THEN
        SET NEW.final_grade = 'D1';
        SET NEW.grade_points = 1;
        SET NEW.grade_description = 'Distinction 1 - Excellent performance';
    ELSEIF NEW.percentage >= 75 THEN
        SET NEW.final_grade = 'D2';
        SET NEW.grade_points = 1;
        SET NEW.grade_description = 'Distinction 2 - Very good performance';
    ELSEIF NEW.percentage >= 70 THEN
        SET NEW.final_grade = 'C3';
        SET NEW.grade_points = 1;
        SET NEW.grade_description = 'Credit 3 - Good performance';
    ELSEIF NEW.percentage >= 60 THEN
        SET NEW.final_grade = 'C4';
        SET NEW.grade_points = 1;
        SET NEW.grade_description = 'Credit 4 - Above average performance';
    ELSEIF NEW.percentage >= 55 THEN
        SET NEW.final_grade = 'C5';
        SET NEW.grade_points = 1;
        SET NEW.grade_description = 'Credit 5 - Average performance';
    ELSEIF NEW.percentage >= 50 THEN
        SET NEW.final_grade = 'C6';
        SET NEW.grade_points = 1;
        SET NEW.grade_description = 'Credit 6 - Below average performance';
    ELSEIF NEW.percentage >= 45 THEN
        SET NEW.final_grade = 'P7';
        SET NEW.grade_points = 0;
        SET NEW.grade_description = 'Pass 7 - Minimum pass';
    ELSEIF NEW.percentage >= 39 THEN
        SET NEW.final_grade = 'P8';
        SET NEW.grade_points = 0;
        SET NEW.grade_description = 'Pass 8 - Weak pass';
    ELSE
        SET NEW.final_grade = 'F9';
        SET NEW.grade_points = 0;
        SET NEW.grade_description = 'Fail 9 - Failure';
    END IF;
END//
DELIMITER ;

-- Trigger to automatically update grade and points when percentage is updated
DELIMITER //
CREATE TRIGGER update_subsidiary_grade_points
BEFORE UPDATE ON a_level_subsidiary_examinations
FOR EACH ROW
BEGIN
    -- Only recalculate if percentage has changed
    IF NEW.percentage != OLD.percentage THEN
        -- Calculate grade and points based on new percentage
        IF NEW.percentage >= 80 THEN
            SET NEW.final_grade = 'D1';
            SET NEW.grade_points = 1;
            SET NEW.grade_description = 'Distinction 1 - Excellent performance';
        ELSEIF NEW.percentage >= 75 THEN
            SET NEW.final_grade = 'D2';
            SET NEW.grade_points = 1;
            SET NEW.grade_description = 'Distinction 2 - Very good performance';
        ELSEIF NEW.percentage >= 70 THEN
            SET NEW.final_grade = 'C3';
            SET NEW.grade_points = 1;
            SET NEW.grade_description = 'Credit 3 - Good performance';
        ELSEIF NEW.percentage >= 60 THEN
            SET NEW.final_grade = 'C4';
            SET NEW.grade_points = 1;
            SET NEW.grade_description = 'Credit 4 - Above average performance';
        ELSEIF NEW.percentage >= 55 THEN
            SET NEW.final_grade = 'C5';
            SET NEW.grade_points = 1;
            SET NEW.grade_description = 'Credit 5 - Average performance';
        ELSEIF NEW.percentage >= 50 THEN
            SET NEW.final_grade = 'C6';
            SET NEW.grade_points = 1;
            SET NEW.grade_description = 'Credit 6 - Below average performance';
        ELSEIF NEW.percentage >= 45 THEN
            SET NEW.final_grade = 'P7';
            SET NEW.grade_points = 0;
            SET NEW.grade_description = 'Pass 7 - Minimum pass';
        ELSEIF NEW.percentage >= 39 THEN
            SET NEW.final_grade = 'P8';
            SET NEW.grade_points = 0;
            SET NEW.grade_description = 'Pass 8 - Weak pass';
        ELSE
            SET NEW.final_grade = 'F9';
            SET NEW.grade_points = 0;
            SET NEW.grade_description = 'Fail 9 - Failure';
        END IF;
    END IF;
END//
DELIMITER ;

-- Trigger to automatically calculate grade and points for A-Level Principal subjects based on final score
DELIMITER //
CREATE TRIGGER calculate_principal_grade_points
BEFORE INSERT ON a_level_principal_competency_grades
FOR EACH ROW
BEGIN
    -- Calculate grade and points based on final score percentage
    IF NEW.final_score IS NOT NULL THEN
        IF NEW.final_score >= 80 THEN
            SET NEW.final_grade = 'A';
            SET NEW.grade_descriptor = 'Excellent - Demonstrates exceptional competency by applying knowledge and skills innovatively and creatively in real-life situations';
        ELSEIF NEW.final_score >= 70 THEN
            SET NEW.final_grade = 'B+';
            SET NEW.grade_descriptor = 'Very Good Plus - Demonstrates very good competency by effectively applying acquired knowledge and skills in real-life situations';
        ELSEIF NEW.final_score >= 60 THEN
            SET NEW.final_grade = 'B';
            SET NEW.grade_descriptor = 'Good - Demonstrates good competency by adequately applying acquired knowledge and skills in real-life situations';
        ELSEIF NEW.final_score >= 50 THEN
            SET NEW.final_grade = 'C+';
            SET NEW.grade_descriptor = 'Satisfactory Plus - Demonstrates satisfactory competency by applying acquired knowledge and skills in real-life situations';
        ELSEIF NEW.final_score >= 45 THEN
            SET NEW.final_grade = 'C';
            SET NEW.grade_descriptor = 'Satisfactory - Demonstrates minimum satisfactory competency in applying acquired knowledge and skills in real-life situations';
        ELSEIF NEW.final_score >= 40 THEN
            SET NEW.final_grade = 'D';
            SET NEW.grade_descriptor = 'Basic - Demonstrates basic competency in applying acquired knowledge and skills in real-life situations';
        ELSE
            SET NEW.final_grade = 'E';
            SET NEW.grade_descriptor = 'Elementary - Demonstrates below basic competency in applying acquired knowledge and skills in real-life situations';
        END IF;
    END IF;
END//
DELIMITER ;

-- Trigger to automatically update grade and points when final score is updated
DELIMITER //
CREATE TRIGGER update_principal_grade_points
BEFORE UPDATE ON a_level_principal_competency_grades
FOR EACH ROW
BEGIN
    -- Only recalculate if final score has changed
    IF NEW.final_score != OLD.final_score AND NEW.final_score IS NOT NULL THEN
        -- Calculate grade and points based on new final score
        IF NEW.final_score >= 80 THEN
            SET NEW.final_grade = 'A';
            SET NEW.grade_descriptor = 'Excellent - Demonstrates exceptional competency by applying knowledge and skills innovatively and creatively in real-life situations';
        ELSEIF NEW.final_score >= 70 THEN
            SET NEW.final_grade = 'B+';
            SET NEW.grade_descriptor = 'Very Good Plus - Demonstrates very good competency by effectively applying acquired knowledge and skills in real-life situations';
        ELSEIF NEW.final_score >= 60 THEN
            SET NEW.final_grade = 'B';
            SET NEW.grade_descriptor = 'Good - Demonstrates good competency by adequately applying acquired knowledge and skills in real-life situations';
        ELSEIF NEW.final_score >= 50 THEN
            SET NEW.final_grade = 'C+';
            SET NEW.grade_descriptor = 'Satisfactory Plus - Demonstrates satisfactory competency by applying acquired knowledge and skills in real-life situations';
        ELSEIF NEW.final_score >= 45 THEN
            SET NEW.final_grade = 'C';
            SET NEW.grade_descriptor = 'Satisfactory - Demonstrates minimum satisfactory competency in applying acquired knowledge and skills in real-life situations';
        ELSEIF NEW.final_score >= 40 THEN
            SET NEW.final_grade = 'D';
            SET NEW.grade_descriptor = 'Basic - Demonstrates basic competency in applying acquired knowledge and skills in real-life situations';
        ELSE
            SET NEW.final_grade = 'E';
            SET NEW.grade_descriptor = 'Elementary - Demonstrates below basic competency in applying acquired knowledge and skills in real-life situations';
        END IF;
    END IF;
END//
DELIMITER ;

-- Trigger to automatically calculate grade and points for O-Level subjects based on final score
DELIMITER //
CREATE TRIGGER calculate_o_level_grade_points
BEFORE INSERT ON o_level_competency_grades
FOR EACH ROW
BEGIN
    -- Calculate grade and points based on final score percentage
    IF NEW.final_score IS NOT NULL THEN
        IF NEW.final_score >= 80 THEN
            SET NEW.final_grade = 'A';
            SET NEW.grade_descriptor = 'Excellent - Demonstrates exceptional competency by applying knowledge and skills innovatively and creatively in real-life situations';
        ELSEIF NEW.final_score >= 70 THEN
            SET NEW.final_grade = 'B+';
            SET NEW.grade_descriptor = 'Very Good Plus - Demonstrates very good competency by effectively applying acquired knowledge and skills in real-life situations';
        ELSEIF NEW.final_score >= 60 THEN
            SET NEW.final_grade = 'B';
            SET NEW.grade_descriptor = 'Good - Demonstrates good competency by adequately applying acquired knowledge and skills in real-life situations';
        ELSEIF NEW.final_score >= 50 THEN
            SET NEW.final_grade = 'C+';
            SET NEW.grade_descriptor = 'Satisfactory Plus - Demonstrates satisfactory competency by applying acquired knowledge and skills in real-life situations';
        ELSEIF NEW.final_score >= 45 THEN
            SET NEW.final_grade = 'C';
            SET NEW.grade_descriptor = 'Satisfactory - Demonstrates minimum satisfactory competency in applying acquired knowledge and skills in real-life situations';
        ELSEIF NEW.final_score >= 40 THEN
            SET NEW.final_grade = 'D';
            SET NEW.grade_descriptor = 'Basic - Demonstrates basic competency in applying acquired knowledge and skills in real-life situations';
        ELSE
            SET NEW.final_grade = 'E';
            SET NEW.grade_descriptor = 'Elementary - Demonstrates below basic competency in applying acquired knowledge and skills in real-life situations';
        END IF;
    END IF;
END//
DELIMITER ;

-- Trigger to automatically update O-Level grade and points when final score is updated
DELIMITER //
CREATE TRIGGER update_o_level_grade_points
BEFORE UPDATE ON o_level_competency_grades
FOR EACH ROW
BEGIN
    -- Only recalculate if final score has changed
    IF NEW.final_score != OLD.final_score AND NEW.final_score IS NOT NULL THEN
        -- Calculate grade and points based on new final score
        IF NEW.final_score >= 80 THEN
            SET NEW.final_grade = 'A';
            SET NEW.grade_descriptor = 'Excellent - Demonstrates exceptional competency by applying knowledge and skills innovatively and creatively in real-life situations';
        ELSEIF NEW.final_score >= 70 THEN
            SET NEW.final_grade = 'B+';
            SET NEW.grade_descriptor = 'Very Good Plus - Demonstrates very good competency by effectively applying acquired knowledge and skills in real-life situations';
        ELSEIF NEW.final_score >= 60 THEN
            SET NEW.final_grade = 'B';
            SET NEW.grade_descriptor = 'Good - Demonstrates good competency by adequately applying acquired knowledge and skills in real-life situations';
        ELSEIF NEW.final_score >= 50 THEN
            SET NEW.final_grade = 'C+';
            SET NEW.grade_descriptor = 'Satisfactory Plus - Demonstrates satisfactory competency by applying acquired knowledge and skills in real-life situations';
        ELSEIF NEW.final_score >= 45 THEN
            SET NEW.final_grade = 'C';
            SET NEW.grade_descriptor = 'Satisfactory - Demonstrates minimum satisfactory competency in applying acquired knowledge and skills in real-life situations';
        ELSEIF NEW.final_score >= 40 THEN
            SET NEW.final_grade = 'D';
            SET NEW.grade_descriptor = 'Basic - Demonstrates basic competency in applying acquired knowledge and skills in real-life situations';
        ELSE
            SET NEW.final_grade = 'E';
            SET NEW.grade_descriptor = 'Elementary - Demonstrates below basic competency in applying acquired knowledge and skills in real-life situations';
        END IF;
    END IF;
END//
DELIMITER ;

-- Stored procedure to calculate UACE points for a student
DELIMITER //
CREATE PROCEDURE calculate_student_uace_points(
    IN p_student_id INT,
    IN p_academic_year_id INT,
    IN p_term_id INT
)
BEGIN
    DECLARE v_principal_1_id INT DEFAULT NULL;
    DECLARE v_principal_1_grade VARCHAR(2) DEFAULT NULL;
    DECLARE v_principal_1_points INT DEFAULT 0;

    DECLARE v_principal_2_id INT DEFAULT NULL;
    DECLARE v_principal_2_grade VARCHAR(2) DEFAULT NULL;
    DECLARE v_principal_2_points INT DEFAULT 0;

    DECLARE v_principal_3_id INT DEFAULT NULL;
    DECLARE v_principal_3_grade VARCHAR(2) DEFAULT NULL;
    DECLARE v_principal_3_points INT DEFAULT 0;

    DECLARE v_subsidiary_id INT DEFAULT NULL;
    DECLARE v_subsidiary_grade VARCHAR(2) DEFAULT NULL;
    DECLARE v_subsidiary_points INT DEFAULT 0;

    DECLARE v_total_principal_points INT DEFAULT 0;
    DECLARE v_total_uace_points INT DEFAULT 0;
    DECLARE v_performance_level VARCHAR(20) DEFAULT 'Needs Improvement';

    -- Get best Principal subject (A=6pts, B+=5pts, B=4pts, C+=3pts, C=2pts, D=1pt, E=0pts)
    SELECT subject_id, final_grade,
           CASE final_grade
               WHEN 'A' THEN 6
               WHEN 'B+' THEN 5
               WHEN 'B' THEN 4
               WHEN 'C+' THEN 3
               WHEN 'C' THEN 2
               WHEN 'D' THEN 1
               WHEN 'E' THEN 0
               ELSE 0
           END
    INTO v_principal_1_id, v_principal_1_grade, v_principal_1_points
    FROM a_level_principal_competency_grades apcg
    WHERE apcg.student_id = p_student_id
      AND apcg.academic_year_id = p_academic_year_id
      AND apcg.term_id = p_term_id
      AND apcg.final_grade IS NOT NULL
    ORDER BY CASE final_grade
                 WHEN 'A' THEN 6
                 WHEN 'B+' THEN 5
                 WHEN 'B' THEN 4
                 WHEN 'C+' THEN 3
                 WHEN 'C' THEN 2
                 WHEN 'D' THEN 1
                 WHEN 'E' THEN 0
                 ELSE 0
             END DESC
    LIMIT 1;

    -- Get second best Principal subject
    SELECT subject_id, final_grade,
           CASE final_grade
               WHEN 'A' THEN 6
               WHEN 'B+' THEN 5
               WHEN 'B' THEN 4
               WHEN 'C+' THEN 3
               WHEN 'C' THEN 2
               WHEN 'D' THEN 1
               WHEN 'E' THEN 0
               ELSE 0
           END
    INTO v_principal_2_id, v_principal_2_grade, v_principal_2_points
    FROM a_level_principal_competency_grades apcg
    WHERE apcg.student_id = p_student_id
      AND apcg.academic_year_id = p_academic_year_id
      AND apcg.term_id = p_term_id
      AND apcg.final_grade IS NOT NULL
      AND apcg.subject_id != COALESCE(v_principal_1_id, 0)
    ORDER BY CASE final_grade
                 WHEN 'A' THEN 6
                 WHEN 'B+' THEN 5
                 WHEN 'B' THEN 4
                 WHEN 'C+' THEN 3
                 WHEN 'C' THEN 2
                 WHEN 'D' THEN 1
                 WHEN 'E' THEN 0
                 ELSE 0
             END DESC
    LIMIT 1;

    -- Get third best Principal subject
    SELECT subject_id, final_grade,
           CASE final_grade
               WHEN 'A' THEN 6
               WHEN 'B+' THEN 5
               WHEN 'B' THEN 4
               WHEN 'C+' THEN 3
               WHEN 'C' THEN 2
               WHEN 'D' THEN 1
               WHEN 'E' THEN 0
               ELSE 0
           END
    INTO v_principal_3_id, v_principal_3_grade, v_principal_3_points
    FROM a_level_principal_competency_grades apcg
    WHERE apcg.student_id = p_student_id
      AND apcg.academic_year_id = p_academic_year_id
      AND apcg.term_id = p_term_id
      AND apcg.final_grade IS NOT NULL
      AND apcg.subject_id NOT IN (COALESCE(v_principal_1_id, 0), COALESCE(v_principal_2_id, 0))
    ORDER BY CASE final_grade
                 WHEN 'A' THEN 6
                 WHEN 'B+' THEN 5
                 WHEN 'B' THEN 4
                 WHEN 'C+' THEN 3
                 WHEN 'C' THEN 2
                 WHEN 'D' THEN 1
                 WHEN 'E' THEN 0
                 ELSE 0
             END DESC
    LIMIT 1;

    -- Get Subsidiary subject grade and points
    SELECT subject_id, final_grade, grade_points
    INTO v_subsidiary_id, v_subsidiary_grade, v_subsidiary_points
    FROM a_level_subsidiary_examinations
    WHERE student_id = p_student_id
      AND academic_year_id = p_academic_year_id
      AND term_id = p_term_id
      AND final_grade IS NOT NULL
    LIMIT 1;

    -- Calculate totals
    SET v_total_principal_points = COALESCE(v_principal_1_points, 0) + COALESCE(v_principal_2_points, 0) + COALESCE(v_principal_3_points, 0);
    SET v_total_uace_points = v_total_principal_points + COALESCE(v_subsidiary_points, 0);

    -- Determine performance level
    IF v_total_uace_points >= 15 THEN
        SET v_performance_level = 'Excellent';
    ELSEIF v_total_uace_points >= 12 THEN
        SET v_performance_level = 'Very Good';
    ELSEIF v_total_uace_points >= 8 THEN
        SET v_performance_level = 'Good';
    ELSEIF v_total_uace_points >= 4 THEN
        SET v_performance_level = 'Satisfactory';
    ELSE
        SET v_performance_level = 'Needs Improvement';
    END IF;

    -- Insert or update UACE points record
    INSERT INTO a_level_uace_points (
        student_id, academic_year_id, term_id,
        principal_subject_1_id, principal_subject_1_grade, principal_subject_1_points,
        principal_subject_2_id, principal_subject_2_grade, principal_subject_2_points,
        principal_subject_3_id, principal_subject_3_grade, principal_subject_3_points,
        total_principal_points,
        subsidiary_subject_id, subsidiary_grade, subsidiary_points,
        total_uace_points, uace_performance_level,
        calculated_at
    )
    VALUES (
        p_student_id, p_academic_year_id, p_term_id,
        v_principal_1_id, v_principal_1_grade, v_principal_1_points,
        v_principal_2_id, v_principal_2_grade, v_principal_2_points,
        v_principal_3_id, v_principal_3_grade, v_principal_3_points,
        v_total_principal_points,
        v_subsidiary_id, v_subsidiary_grade, v_subsidiary_points,
        v_total_uace_points, v_performance_level,
        NOW()
    )
    ON DUPLICATE KEY UPDATE
        principal_subject_1_id = VALUES(principal_subject_1_id),
        principal_subject_1_grade = VALUES(principal_subject_1_grade),
        principal_subject_1_points = VALUES(principal_subject_1_points),
        principal_subject_2_id = VALUES(principal_subject_2_id),
        principal_subject_2_grade = VALUES(principal_subject_2_grade),
        principal_subject_2_points = VALUES(principal_subject_2_points),
        principal_subject_3_id = VALUES(principal_subject_3_id),
        principal_subject_3_grade = VALUES(principal_subject_3_grade),
        principal_subject_3_points = VALUES(principal_subject_3_points),
        total_principal_points = VALUES(total_principal_points),
        subsidiary_subject_id = VALUES(subsidiary_subject_id),
        subsidiary_grade = VALUES(subsidiary_grade),
        subsidiary_points = VALUES(subsidiary_points),
        total_uace_points = VALUES(total_uace_points),
        uace_performance_level = VALUES(uace_performance_level),
        calculated_at = VALUES(calculated_at),
        updated_at = NOW();

END//
DELIMITER ;

-- =============================================
-- 12. INITIAL DATA POPULATION
-- =============================================

-- Insert default competency levels
INSERT IGNORE INTO competency_levels
(level_code, level_name, level_descriptor, min_score, max_score)
VALUES
(0, 'Absent', 'Student was absent during assessment', 0.0, 0.0),
(1, 'Basic', 'Demonstrates basic understanding and skills', 1.0, 1.0),
(2, 'Moderate', 'Demonstrates moderate understanding and skills', 2.0, 2.0),
(3, 'Accomplished', 'Demonstrates accomplished understanding and skills', 3.0, 3.0);





-- Insert school settings
INSERT IGNORE INTO school_settings
(setting_key, setting_value, setting_type, category, description, is_editable)
VALUES
('school_name', 'Nyabikoni Secondary School', 'string', 'school_info', 'Official school name', TRUE),
('school_motto', 'Strive for Excellence', 'string', 'school_info', 'School motto', TRUE),
('school_address', 'P.O. Box 304 Kabale Uganda', 'string', 'school_info', 'School postal address', TRUE),
('school_email', '<EMAIL>', 'string', 'school_info', 'School email address', TRUE),
('school_website', 'www.nyabikoniss.ac.ug', 'string', 'school_info', 'School website URL', TRUE),
('school_logo_path', '/assets/images/logo.png', 'string', 'general', 'Path to school logo file', TRUE),
('school_contacts', '+256 772 655 176, +256 703 599 882, +256 775 475 629', 'string', 'school_info', 'School contact numbers', TRUE);


-- Insert default admin user FIRST (required for foreign key references)
INSERT IGNORE INTO system_users
(username, email, password, first_name, last_name, role, is_active, created_at)
VALUES
('admin', '<EMAIL>', '$2b$10$cDybGDbPesavR6qRabZty.ARDwQGJ5pf1UqSyvmc32Oc19Ci9feaK', 'John', 'Doe', 'system_admin', TRUE, NOW());

-- Insert default education levels data (after admin user exists)
INSERT IGNORE INTO education_levels
(code, name, display_name, sort_order, is_active, created_by_id)
VALUES
('o_level', 'O Level', 'Ordinary Level', 1, TRUE, 1),
('a_level', 'A Level', 'Advanced Level', 2, TRUE, 1);

-- Insert default class levels data (after education levels exist)
INSERT IGNORE INTO class_levels
(code, name, display_name, education_level_id, sort_order, streams_optional, is_active, created_by_id)
VALUES
('s1', 'S.1', 'Senior One', 1, 1, TRUE, TRUE, 1),
('s2', 'S.2', 'Senior Two', 1, 2, TRUE, TRUE, 1),
('s3', 'S.3', 'Senior Three', 1, 3, TRUE, TRUE, 1),
('s4', 'S.4', 'Senior Four', 1, 4, TRUE, TRUE, 1),
('s5', 'S.5', 'Senior Five', 2, 5, FALSE, TRUE, 1),
('s6', 'S.6', 'Senior Six', 2, 6, FALSE, TRUE, 1);

-- Note: Default password is 'admin123' - should be changed on first login




-- Insert O-Level competency grading scale
INSERT IGNORE INTO o_level_grading_scale
(competency_level, competency_description, min_score, max_score, grade_comment, is_pass, created_by_id)
VALUES
(0, 'Absent', 0.00, 0.00, 'Absent - No Learning Outcomes achieved. Learner was absent', FALSE, 1),
(1, 'Basic', 0.10, 1.49, 'Basic - Some learning outcomes achieved, but not sufficient for overall achievement.', TRUE, 1),
(2, 'Moderate', 1.50, 2.49, 'Moderate - Most learning outcomes achieved, enough for overall learning achievement.', TRUE, 1),
(3, 'Accomplished', 2.50, 3.00, 'Accomplished - All learning outcomes achieved with ease.', TRUE, 1);

-- Insert A-Level Principal subjects competency grading scale (same as O-Level)
INSERT IGNORE INTO a_level_principal_grading_scale
(competency_level, competency_description, min_score, max_score, is_pass, created_by_id)
VALUES
(1, 'Basic - Demonstrates basic understanding and skills', 1.0, 1.0, TRUE, 1),
(2, 'Moderate - Demonstrates moderate understanding and skills', 2.0, 2.0, TRUE, 1),
(3, 'Accomplished - Demonstrates accomplished understanding and skills', 3.0, 3.0, TRUE, 1),
(4, 'Exceptional - Demonstrates exceptional understanding and skills', 4.0, 4.0, TRUE, 1);

-- Insert A-Level Subsidiary subjects traditional grading scale (D1-F9) - Updated boundaries
INSERT IGNORE INTO a_level_subsidiary_grading_scale
(grade_code, grade_description, min_score, max_score, points, is_pass, created_by_id)
VALUES
('D1', 'Distinction 1 - Excellent performance', 80, 100, 1, TRUE, 1),
('D2', 'Distinction 2 - Very good performance', 75, 79, 1, TRUE, 1),
('C3', 'Credit 3 - Good performance', 70, 74, 1, TRUE, 1),
('C4', 'Credit 4 - Above average performance', 60, 69, 1, TRUE, 1),
('C5', 'Credit 5 - Average performance', 55, 59, 1, TRUE, 1),
('C6', 'Credit 6 - Below average performance', 50, 54, 1, TRUE, 1),
('P7', 'Pass 7 - Minimum pass', 45, 49, 0, TRUE, 1),
('P8', 'Pass 8 - Weak pass', 39, 44, 0, TRUE, 1),
('F9', 'Fail 9 - Failure', 0, 38, 0, FALSE, 1);

-- Insert O-Level Competency-Based Grading Scale
INSERT IGNORE INTO o_level_grade_boundaries
(grade_letter, min_percentage, max_percentage, grade_descriptor)
VALUES
('A', 80, 100, 'Excellent'),
('B+', 70, 79, 'Very Good Plus'),
('B', 60, 69, 'Good'),
('C+', 50, 59, 'Satisfactory Plus'),
('C', 45, 49, 'Satisfactory'),
('D', 40, 44, 'Basic'),
('E', 0, 39, 'Elementary');

-- Insert A-Level Principal Subjects Competency-Based Grading Scale (Same as O-Level)
INSERT IGNORE INTO a_level_principal_grade_boundaries
(grade_letter, min_percentage, max_percentage, grade_descriptor, points)
VALUES
('A', 80, 100, 'Excellent', 6),
('B+', 70, 79, 'Very Good Plus', 5),
('B', 60, 69, 'Good', 4),
('C+', 50, 59, 'Satisfactory Plus', 3),
('C', 45, 49, 'Satisfactory', 2),
('D', 40, 44, 'Basic', 1),
('E', 0, 39, 'Elementary', 0);


-- =============================================
-- UGANDA O-LEVEL CURRICULUM FRAMEWORK - OFFICIAL STRUCTURE
-- =============================================

-- S1-S2 COMPULSORY SUBJECTS (10 subjects - non-religious) + subjects that change status
INSERT IGNORE INTO o_level_subjects
(name, short_name, subject_type, uneb_code, created_by_id)
VALUES
('English', 'ENG', 'Compulsory', '112', 1),
('Mathematics', 'MATH', 'Compulsory', '456', 1),
('History & Political Education', 'HPE', 'Compulsory', '241', 1),
('Geography', 'GEOG', 'Compulsory', '273', 1),
('Physics', 'PHY', 'Compulsory', '535', 1),
('Biology', 'BIO', 'Compulsory', '553', 1),
('Chemistry', 'CHEM', 'Compulsory', '545', 1),
-- Subjects that are compulsory in S1-S2 but become elective in S3-S4
('Physical Education', 'PE', 'Practical (pre-vocational)', '631', 1),
('Entrepreneurship', 'ENT', 'Practical (pre-vocational)', '845', 1),
('Kiswahili', 'KIS', 'Language', '336', 1);

-- RELIGIOUS EDUCATION (Choose 1 - S1-S2 compulsory to choose one)
INSERT IGNORE INTO o_level_subjects
(name, short_name, subject_type, uneb_code, created_by_id)
VALUES
('Christian Religious Education', 'CRE', 'Religious Education', '140', 1),
('Islamic Religious Education', 'IRE', 'Religious Education', '485', 1);

-- PRACTICAL (PRE-VOCATIONAL) ELECTIVES (S1-S2: choose 1, S3-S4: elective)
INSERT IGNORE INTO o_level_subjects
(name, short_name, subject_type, uneb_code, created_by_id)
VALUES
('Agriculture', 'AGRIC', 'Practical (pre-vocational)', 'AGRIC', 1),
('Information Communication Technology', 'ICT', 'Practical (pre-vocational)', 'ICT', 1),
('Technology and Design', 'TD', 'Practical (pre-vocational)', 'TD', 1),
('Nutrition and Food Technology', 'NFT', 'Practical (pre-vocational)', 'NFT', 1),
('Art and Design', 'ART', 'Practical (pre-vocational)', 'ART', 1),
('Performing Arts', 'PA', 'Practical (pre-vocational)', 'PA', 1);

-- LANGUAGE ELECTIVES (S1-S2: choose 1, S3-S4: elective)
INSERT IGNORE INTO o_level_subjects
(name, short_name, subject_type, uneb_code, created_by_id)
VALUES
('French', 'FREN', 'Language', 'FREN', 1),
('German', 'GERM', 'Language', 'GERM', 1),
('Latin', 'LAT', 'Language', 'LAT', 1),
('Arabic', 'ARAB', 'Language', 'ARAB', 1),
('Chinese', 'CHI', 'Language', 'CHI', 1),
('Literature in English', 'LIT', 'Language', 'LIT', 1),
('Luganda', 'LUG', 'Language', 'LUG', 1),
('Lusoga', 'LUS', 'Language', 'LUS', 1),
('Runyankore-Rukiga', 'RUR', 'Language', 'RUR', 1),
('Runyoro-Rutoro', 'RRT', 'Language', 'RRT', 1),
('Luo', 'LUO', 'Language', 'LUO', 1),
('Ateso', 'ATES', 'Language', 'ATES', 1),
('Lugbara', 'LUGB', 'Language', 'LUGB', 1),
('Dhopadhola', 'DHO', 'Language', 'DHO', 1),
('Lumasaaba', 'LUM', 'Language', 'LUM', 1),
('Ugandan Sign Language', 'USL', 'Language', 'USL', 1);

-- Insert O-Level Subject-Class relationships
-- Compulsory subjects for all classes (S1-S4)
INSERT IGNORE INTO o_level_subject_classes (subject_id, class_level_id, is_compulsory, is_elective)
SELECT s.id, cl.id, TRUE, FALSE
FROM o_level_subjects s
CROSS JOIN class_levels cl
JOIN education_levels el ON cl.education_level_id = el.id
WHERE s.short_name IN ('ENG', 'MATH', 'HPE', 'GEOG', 'PHY', 'BIO', 'CHEM')
AND el.code = 'o_level';

-- Subjects that are compulsory in S1-S2 but elective in S3-S4
INSERT IGNORE INTO o_level_subject_classes (subject_id, class_level_id, is_compulsory, is_elective)
SELECT s.id, cl.id, TRUE, FALSE
FROM o_level_subjects s
CROSS JOIN class_levels cl
JOIN education_levels el ON cl.education_level_id = el.id
WHERE s.short_name IN ('PE', 'ENT', 'KIS')
AND el.code = 'o_level'
AND cl.code IN ('s1', 's2');

INSERT IGNORE INTO o_level_subject_classes (subject_id, class_level_id, is_compulsory, is_elective)
SELECT s.id, cl.id, FALSE, TRUE
FROM o_level_subjects s
CROSS JOIN class_levels cl
JOIN education_levels el ON cl.education_level_id = el.id
WHERE s.short_name IN ('PE', 'ENT', 'KIS')
AND el.code = 'o_level'
AND cl.code IN ('s3', 's4');

-- Religious Education subjects (elective for all O-Level)
INSERT IGNORE INTO o_level_subject_classes (subject_id, class_level_id, is_compulsory, is_elective)
SELECT s.id, cl.id, FALSE, TRUE
FROM o_level_subjects s
CROSS JOIN class_levels cl
JOIN education_levels el ON cl.education_level_id = el.id
WHERE s.subject_type = 'Religious Education'
AND el.code = 'o_level';

-- Practical and Language subjects (elective for all O-Level)
INSERT IGNORE INTO o_level_subject_classes (subject_id, class_level_id, is_compulsory, is_elective)
SELECT s.id, cl.id, FALSE, TRUE
FROM o_level_subjects s
CROSS JOIN class_levels cl
JOIN education_levels el ON cl.education_level_id = el.id
WHERE s.subject_type IN ('Practical (pre-vocational)', 'Language')
AND el.code = 'o_level';

-- Insert O-Level subject selection rules according to Uganda Curriculum Framework
INSERT IGNORE INTO o_level_selection_rules
(class_level_id, total_subjects_required, compulsory_subjects_count, elective_subjects_count, religious_education_required, description)
SELECT cl.id, 12, 10, 2, TRUE, CONCAT(cl.name, ': 10 compulsory + 1 religious education choice (CRE or IRE) + 1 elective (PRACTICAL (PRE-VOCATIONAL) or LANGUAGE) = 12 total')
FROM class_levels cl
WHERE cl.code IN ('s1', 's2');

INSERT IGNORE INTO o_level_selection_rules
(class_level_id, total_subjects_required, compulsory_subjects_count, elective_subjects_count, religious_education_required, description)
SELECT cl.id, 9, 7, 2, FALSE, CONCAT(cl.name, ': 7 compulsory + 1 or 2 electives (student choice) = 8 or 9 total subjects')
FROM class_levels cl
WHERE cl.code IN ('s3', 's4');

-- Additional rules for S3-S4 students choosing minimum subjects (8 total)
INSERT IGNORE INTO o_level_selection_rules
(class_level_id, total_subjects_required, compulsory_subjects_count, elective_subjects_count, religious_education_required, description)
SELECT cl.id, 8, 7, 1, FALSE, CONCAT(cl.name, ': 7 compulsory + 1 elective (minimum option) = 8 total subjects')
FROM class_levels cl
WHERE cl.code IN ('s3', 's4');

-- Insert A-Level subject selection rules
INSERT IGNORE INTO a_level_selection_rules
(class_level_id, total_subjects_required, compulsory_subjects_count, subsidiary_subjects_count, principal_subjects_count, description)
SELECT cl.id, 5, 1, 1, 3, CONCAT(cl.name, ': 1 compulsory (GP) + 1 subsidiary (SMATH or SICT) + 3 principal subjects = 5 total')
FROM class_levels cl
WHERE cl.code IN ('s5', 's6');

-- =============================================
-- UGANDA O-LEVEL CURRICULUM FRAMEWORK SUMMARY
-- =============================================
--
-- S1-S2 STRUCTURE (Total: 12 subjects)
-- - 10 Compulsory subjects (non-religious)
-- - 1 Religious Education choice (CRE or IRE) - effectively compulsory
-- - 1 Elective subject (Practical or Language)
-- = 12 total subjects
--
-- S3-S4 STRUCTURE (Student Choice: 8 or 9 subjects)
-- - 7 Compulsory subjects (mandatory)
-- - 1 OR 2 Elective subjects (student choice - full flexibility)
-- = 8 total subjects (minimum) OR 9 total subjects (maximum)
--
-- COMPULSORY SUBJECTS:
-- S1-S2: English, Mathematics, HPE, Geography, Physics, Biology, Chemistry, PE, ENT, Kiswahili + 1 Religious Education
-- S3-S4: English, Mathematics, Geography, Physics, Biology, Chemistry, History & Political Education (7 compulsory)
--
-- ELECTIVE CATEGORIES:
-- - Practical (pre-vocational): Agriculture, ICT, TD, NFT, Art & Design, Performing Arts
-- - Language: French, German, Latin, Arabic, Chinese, Literature in English, Local Languages
-- - Religious Education: CRE, IRE (becomes elective in S3-S4)
-- - S1-S2 Compulsory that become S3-S4 Elective: Physical Education, Entrepreneurship, Kiswahili
--
-- SUBJECT PROGRESSION:
-- S1-S2: Students choose 1 religious education (CRE/IRE) + 1 elective (Practical/Language)
-- S3-S4: Students can change BOTH S1-S2 electives (religious education becomes optional)
-- Cannot change the 7 compulsory subjects in S3-S4
--
-- WORKLOAD CHOICE IN S3-S4:
-- Option 1: 7 compulsory + 1 elective = 8 total subjects (lighter workload)
-- Option 2: 7 compulsory + 2 electives = 9 total subjects (standard workload)
--
-- ELECTIVE FLEXIBILITY IN S3-S4:
-- - Can choose any subjects from: Practical (pre-vocational), Language, Religious Education
-- - Former S1-S2 compulsory subjects (PE, ENT, KIS) also available as electives
-- - Full freedom to design academic path based on interests and career goals
--
-- =============================================

-- =============================================
-- UGANDA A-LEVEL CURRICULUM FRAMEWORK (S5-S6)
-- =============================================
--
-- A-LEVEL STRUCTURE (Total: 5 subjects)
-- - 1 Compulsory subject: General Paper (GP)
-- - 1 Subsidiary subject: Choose SMATH or SICT
-- - 3 Principal subjects: Based on combination choice
--
-- SUBSIDIARY SELECTION RULES:
-- - Students taking Principal Mathematics → Must take Subsidiary ICT
-- - Students taking Economics or Agriculture (without Principal Mathematics) → Must take Subsidiary Mathematics
-- - Other combinations → Can choose either SMATH or SICT
--
-- STREAM CLASSIFICATION:
-- - Sciences Stream: Any combination with science subjects (Math, Physics, Chemistry, Biology, Agriculture)
-- - Arts Stream: Combinations focused on humanities subjects
--
-- COMBINATION RULES:
-- - Each combination must have exactly 3 principal subjects
-- - A student can change their combination or subjects only in S5 
--
-- POPULAR COMBINATIONS:
-- Sciences: PCM, PCB, BCM, MCB, MEA
-- Arts: HEG, HEL, HGL, EGL, HED, HGD
-- Mixed: MEG, MEL, GEL
--
-- =============================================

-- COMPULSORY SUBJECT (All students must take)
INSERT IGNORE INTO a_level_subjects
(name, short_name, subject_type, uneb_code, exam_papers, created_by_id)
VALUES
('General Paper', 'GP', 'Subsidiary', 'S101', 1, 1);

-- SUBSIDIARY SUBJECTS (Choose 1: SMATH or SICT)
INSERT IGNORE INTO a_level_subjects
(name, short_name, subject_type, uneb_code, exam_papers, created_by_id)
VALUES
('Subsidiary Mathematics', 'SMATH', 'Subsidiary', 'S475', 1, 1),
('Subsidiary ICT', 'SICT', 'Subsidiary', 'S850', 1, 1);

-- SCIENCE SUBJECTS (Principal)
INSERT IGNORE INTO a_level_subjects
(name, short_name, subject_type, uneb_code, exam_papers, created_by_id)
VALUES
('Principal Mathematics', 'MATH', 'Principal', 'P425', 2, 1),
('Physics', 'PHYS', 'Principal', 'P510', 3, 1),
('Chemistry', 'CHEM', 'Principal', 'P525', 3, 1),
('Biology', 'BIOL', 'Principal', 'P530', 3, 1),
('Agriculture', 'AGRI', 'Principal', 'P527', 3, 1);

-- ARTS & HUMANITIES SUBJECTS (Principal)
INSERT IGNORE INTO a_level_subjects
(name, short_name, subject_type, uneb_code, exam_papers, created_by_id)
VALUES
('History', 'HIST', 'Principal', 'P210', 6, 1),
('Geography', 'GEOG', 'Principal', 'P250', 3, 1),
('Literature in English', 'LIT', 'Principal', 'P215', 2, 1),
('Islamic Religious Education', 'IRE', 'Principal', 'P235', 4, 1),
('Divinity', 'DIV', 'Principal', 'P240', 3, 1);

-- BUSINESS/COMMERCIAL SUBJECTS (Principal)
INSERT IGNORE INTO a_level_subjects
(name, short_name, subject_type, uneb_code, exam_papers, created_by_id)
VALUES
('Economics', 'ECON', 'Principal', 'P220', 2, 1),
('Entrepreneurship Education', 'ENT', 'Principal', 'P230', 2, 1);

-- LOCAL LANGUAGE SUBJECTS (3 papers each)
INSERT IGNORE INTO a_level_subjects
(name, short_name, subject_type, uneb_code, exam_papers, created_by_id)
VALUES
('Luganda', 'LUG', 'Principal', 'P340', 3, 1),
('Lusoga', 'LUS', 'Principal', 'P341', 3, 1),
('Runyankore-Rukiga', 'RUR', 'Principal', 'P342', 3, 1),
('Runyoro-Rutoro', 'RRT', 'Principal', 'P343', 3, 1),
('Luo', 'LUO', 'Principal', 'P344', 3, 1),
('Ateso', 'ATES', 'Principal', 'P345', 3, 1),
('Lugbara', 'LUGB', 'Principal', 'P346', 3, 1),
('Dhopadhola', 'DHO', 'Principal', 'P347', 3, 1),
('Lumasaaba', 'LUM', 'Principal', 'P348', 3, 1),
('Ugandan Sign Language', 'USL', 'Principal', 'P349', 3, 1);

-- INTERNATIONAL LANGUAGE SUBJECTS (2 papers each)
INSERT IGNORE INTO a_level_subjects
(name, short_name, subject_type, uneb_code, exam_papers, created_by_id)
VALUES
('Kiswahili', 'KIS', 'Principal', 'P320', 2, 1),
('French', 'FRE', 'Principal', 'P325', 2, 1),
('Arabic', 'ARA', 'Principal', 'P310', 2, 1),
('German', 'GER', 'Principal', 'P350', 2, 1),
('Latin', 'LAT', 'Principal', 'P355', 2, 1),
('Chinese', 'CHI', 'Principal', 'P360', 2, 1);

-- CREATIVE ARTS SUBJECTS (Practical)
INSERT IGNORE INTO a_level_subjects
(name, short_name, subject_type, uneb_code, exam_papers, created_by_id)
VALUES
('Fine Art', 'ART', 'Principal', 'P615', 2, 1),
('Music', 'MUS', 'Principal', 'P620', 2, 1);

-- TECHNICAL/PRACTICAL SUBJECTS
INSERT IGNORE INTO a_level_subjects
(name, short_name, subject_type, uneb_code, exam_papers, created_by_id)
VALUES
('Technology and Design', 'TD', 'Principal', 'P860', 2, 1),
('Nutrition and Food Technology', 'NFT', 'Principal', 'P870', 2, 1),
('Performing Arts', 'PA', 'Principal', 'P880', 2, 1);

-- Insert A-Level Subject-Class relationships (all A-Level subjects apply to both S5 and S6)
INSERT IGNORE INTO a_level_subject_classes (subject_id, class_level_id, is_compulsory, is_elective)
SELECT s.id, cl.id,
       CASE WHEN s.short_name = 'GP' THEN TRUE ELSE FALSE END as is_compulsory,
       CASE WHEN s.short_name = 'GP' THEN FALSE ELSE TRUE END as is_elective
FROM a_level_subjects s
CROSS JOIN class_levels cl
JOIN education_levels el ON cl.education_level_id = el.id
WHERE el.code = 'a_level';

-- Note: A-Level combinations will be managed through the admin interface
-- No default combinations are inserted - they can be added via the system as needed

-- Insert A-Level subject change rules
INSERT IGNORE INTO a_level_subject_change_rules
(rule_name, class_level_id, change_type, description, created_by_id)
SELECT 'S5 Principal Subject Change', cl.id, 'principal_subject', 'S5 students can change principal subjects with proper assessment', 1
FROM class_levels cl WHERE cl.code = 's5'
UNION ALL
SELECT 'S5 Subsidiary Subject Change', cl.id, 'subsidiary_subject', 'S5 students can change subsidiary subject with proper assessment', 1
FROM class_levels cl WHERE cl.code = 's5'
UNION ALL
SELECT 'S5 Combination Change', cl.id, 'combination', 'S5 students can change entire combination with proper assessment', 1
FROM class_levels cl WHERE cl.code = 's5'
UNION ALL
SELECT 'S6 Principal Change', cl.id, 'principal_subject', 'S6 students can change principal subjects in special circumstances', 1
FROM class_levels cl WHERE cl.code = 's6'
UNION ALL
SELECT 'S6 Subsidiary Change', cl.id, 'subsidiary_subject', 'S6 students can change subsidiary subject in special circumstances', 1
FROM class_levels cl WHERE cl.code = 's6'
UNION ALL
SELECT 'S6 Combination Change', cl.id, 'combination', 'S6 students can change combination in special circumstances', 1
FROM class_levels cl WHERE cl.code = 's6';


-- Note: A-Level streams (Arts and Sciences) are automatically created per academic year via trigger
-- O-Level streams (A, B, C, D) are created by admin through the interface as needed per academic year

-- =============================================
-- DEFAULT CLASSES INSERTION
-- =============================================
-- Insert default classes (S.1 to S.6) for each academic year
-- Note: These will be created for the active academic year when it exists
-- The system admin can create additional class sections (A, B, C, etc.) as needed

-- Classes and streams initialization moved to application startup
-- This ensures proper initialization during system first-time setup
-- Classes and streams are now created during database initialization phase



-- Note:
-- CORRECT PERMANENT CLASS AND STREAM DIVISION STRUCTURE:
-- 1. CLASSES: Exactly 8 permanent classes created once when first academic year is added
--    - O-Level: S.1, S.2, S.3, S.4 (4 base classes for stream divisions)
--    - A-Level: S.5 Sciences, S.5 Arts, S.6 Sciences, S.6 Arts (4 stream-based classes)
-- 2. STREAMS AS DIVISIONS: Streams divide students within the same class level
--    - O-Level: Optional divisions (A, B, C, D) within each level (S.1 A, S.1 B, S.2 A, S.2 B, etc.)
--    - A-Level: Mandatory divisions (Sciences, Arts) based on subject combinations
-- 3. ENROLLMENT MANAGEMENT: Academic year and term relationships managed through student_enrollments table
-- 4. CLASS CREATION PROCESS:
--    - O-Level Base Classes: S.1, S.2, S.3, S.4 (admin can add stream divisions A, B, C, D)
--    - A-Level Stream Classes: S.5 Sciences, S.5 Arts, S.6 Sciences, S.6 Arts (automatic based on combinations)
--    - Stream divisions allow multiple classes per level (e.g., S.1 A + S.1 B = total S.1 students)
-- 5. STREAM ASSIGNMENT LOGIC:
--    - O-Level: Optional stream divisions (students can transfer between S.1 A ↔ S.1 B)
--    - A-Level: Automatic stream assignment based on subject combinations:
--      * Sciences Stream: Students with Principal Mathematics OR any Science Principal subject
--      * Arts Stream: Students with other combinations (History, Geography, Literature, etc.)
--    - Admin can manually override A-Level stream assignments if needed
-- 6. STUDENT ENROLLMENT:
--    - Students are enrolled in specific stream classes for academic years and terms
--    - student_enrollments table manages the academic year/term relationships
--    - Stream transfers within same level are allowed (S.1 A → S.1 B, S.5 Sciences → S.5 Arts)
--    - O-Level: Can enroll in base classes (S.1) or stream divisions (S.1 A, S.1 B)
--    - A-Level: Must enroll in stream-specific classes (S.5 Sciences or S.5 Arts)
-- 7. DASHBOARD DISPLAY:
--    - Total Students per Level: S.1 total = S.1 A + S.1 B + S.1 C + S.1 D students
--    - Class-wise Enrollment: Shows individual stream class enrollments
--    - Level-wise Summary: Shows combined enrollment across all streams within a level
-- 8. STREAM DIVISION EXAMPLES:
--    - O-Level: S.1 (70 students) = S.1 A (30) + S.1 B (25) + S.1 C (15)
--    - A-Level: S.5 (60 students) = S.5 Sciences (35) + S.5 Arts (25)
-- 9. ADMIN FLEXIBILITY:
--    - Can create O-Level Class stream divisions: S.1 A, S.1 B, S.2 A, S.2 B, etc.
--    - Can transfer students between streams within same level
--    - A-Level stream assignment is automatic but can be manually adjusted
--    - All stream classes follow the same permanent structure
-- 10. AUTOMATIC A-LEVEL STREAM ASSIGNMENT:
--    - Sciences: Principal Mathematics OR Physics OR Chemistry OR Biology OR Agriculture
--    - Arts: All other combinations (History, Geography, Literature, Economics, Languages, etc.)
--    - Use determine_a_level_stream() function and get_a_level_class_for_student() procedure
-- 11. STUDENT PROGRESSION WORKFLOWS:
--    - O-Level Promotions: Use o_level_student_promotions table for S.1→S.2→S.3→S.4 progressions
--    - A-Level Registrations: Use a_level_student_registrations table for initial entry into A-Level (S.5 only)
--    - A-Level Promotions: Use a_level_student_promotions table for S.5→S.6 progressions (same admission number)
-- 12. O-LEVEL TO A-LEVEL TRANSITION (Internal Students):
--    - Step 1: Change O-Level student status to "completed" in student_status_changes table
--    - Step 2: Update student admission_number to new A-Level admission number
--    - Step 3: Record A-Level registration in a_level_student_registrations table (same student_id)
--    - Step 4: Enroll student in appropriate S.5 class based on subject combination
--    - Result: Same student record, new admission number, preserves all O-Level academic history
-- 13. A-LEVEL REGISTRATION TYPES (for initial S.5 entry):
--    - internal_graduate: S.4 graduate from same school (same student record, new admission number)
--    - external_transfer: Transfer student from another school after Senior 4 (new student record)
-- 14. A-LEVEL PROGRESSION:
--    - Registration: Entry into A-Level system (S.5) with new admission number
--    - Promotion: S.5 → S.6 progression with same admission number (traditional promotion)
-- 15. DATA PRESERVATION:
--    - Internal graduates: All O-Level data preserved (grades, assessments, enrollments)
--    - External transfers: Only A-Level data stored (no previous academic history)
--    - Academic history linkage maintained through admission number changes
