// AIMS Subjects Management Components
// Comprehensive subjects management system for O-Level and A-Level

// Uses global API services: window.SubjectsAPI, window.CombinationsAPI
// Uses global config: window.AIMSConfig
// Uses environment configuration: ../config/environment.js

const SubjectsManagementComponents = {
  // Component state
  state: {
    subjects: [],
    combinations: [],
    loading: false,
    filters: {
      level: '',
      subjectType: '',
      search: ''
    }
  },

  // Initialize component
  async init() {
    await this.loadInitialData();
  },

  // Load initial data
  async loadInitialData() {
    try {
      this.state.loading = true;

      console.log('🔄 Loading subjects management data...');

      // Use the API services with proper error handling
      const [oLevelSubjects, aLevelSubjects, combinations] = await Promise.all([
        window.SubjectsAPI.oLevel.getAll(),
        window.SubjectsAPI.aLevel.getAll(),
        window.CombinationsAPI.getAll()
      ]);

      // Combine subjects with level indicators
      const allSubjects = [
        ...(oLevelSubjects.data || []).map(s => ({ ...s, level: 'o_level' })),
        ...(aLevelSubjects.data || []).map(s => ({ ...s, level: 'a_level' }))
      ];

      this.state.subjects = { success: true, data: allSubjects };
      this.state.combinations = combinations;

      if (window.AIMSConfig && window.AIMSConfig.get('development.debugMode')) {
        console.log('✅ Subjects data loaded:', { oLevelSubjects, aLevelSubjects, combinations });
      }

    } catch (error) {
      console.error('❌ Failed to load initial data:', error);
      if (window.AIMSConfig && window.AIMSConfig.get('development.debugMode')) {
        console.error('Debug: Subjects data loading error details:', error);
      }
      // Show error notification if available
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
        window.AIMSDesignSystem.notifications.show('Failed to load subjects data', 'error');
      }
    } finally {
      this.state.loading = false;
    }
  }
};

// O-Level Subjects Component
const OLevelSubjectsComponent = {
  // Render O-Level subjects interface
  render() {
    return `
      <div class="space-y-6">
        ${AIMSDesignSystem.layouts.pageHeader(
          'O-Level Subjects',
          'Manage O-Level subjects for Senior 1-4 classes',
          [
            { icon: 'fas fa-book', value: this.getOLevelSubjectsCount(), label: 'O-Level Subjects', color: 'blue' },
            { icon: 'fas fa-star', value: this.getCompulsorySubjectsCount(), label: 'Compulsory', color: 'green' }
          ]
        )}

        <!-- Subject Management Actions -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">O-Level Subject Management</h3>
            ${AIMSDesignSystem.forms.button('add-subject', 'Add New Subject', 'primary', {
              icon: 'fas fa-plus',
              onclick: 'OLevelSubjectsComponent.showAddSubjectModal()'
            })}
          </div>

          <!-- Filters -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            ${AIMSDesignSystem.forms.input('search_subjects', 'Search Subjects', '', {
              placeholder: 'Search by name or code...',
              icon: 'fas fa-search'
            })}
            ${AIMSDesignSystem.forms.select('filter_subject_type', 'Subject Type', [
              { value: '', label: 'All Types' },
              { value: 'Compulsory', label: 'Compulsory' },
              { value: 'Language', label: 'Language' },
              { value: 'Practical (pre-vocational)', label: 'Practical (pre-vocational)' },
              { value: 'Religious Education', label: 'Religious Education' }
            ], '')}
            ${AIMSDesignSystem.forms.select('filter_applicable_levels', 'Applicable Levels', [
              { value: '', label: 'All Levels' },
              { value: 's1', label: 'S.1' },
              { value: 's2', label: 'S.2' },
              { value: 's3', label: 'S.3' },
              { value: 's4', label: 'S.4' }
            ], '')}
          </div>
        </div>

        <!-- Subjects Table -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">O-Level Subjects</h3>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UNEB Code</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Applicable Levels</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody id="o-level-subjects-table-body" class="bg-white divide-y divide-gray-200">
                <!-- Subjects will be populated here -->
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Add Subject Modal -->
      <div id="add-o-level-subject-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-xl bg-white">
          <div class="mt-3">
            <div class="flex items-center justify-between pb-4 border-b border-gray-200">
              <h3 class="text-lg font-semibold text-gray-900">Add O-Level Subject</h3>
              <button onclick="OLevelSubjectsComponent.closeAddSubjectModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
              </button>
            </div>
            <form id="add-o-level-subject-form" class="mt-6 space-y-6">
              <!-- Hidden fields for system admin tracking -->
              <input type="hidden" id="o_level_created_by_id" name="created_by_id" value="">

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                ${AIMSDesignSystem.forms.input('name', 'Subject Name', '', {
                  required: true,
                  placeholder: 'e.g., Mathematics'
                })}
                ${AIMSDesignSystem.forms.input('short_name', 'Short Name/Code', '', {
                  required: true,
                  placeholder: 'e.g., MATH'
                })}
                ${AIMSDesignSystem.forms.select('subject_type', 'Subject Type', [
                  { value: '', label: 'Select Subject Type' },
                  { value: 'Compulsory', label: 'Compulsory' },
                  { value: 'Language', label: 'Language' },
                  { value: 'Practical (pre-vocational)', label: 'Practical (pre-vocational)' },
                  { value: 'Religious Education', label: 'Religious Education' }
                ], '', { required: true })}
                ${AIMSDesignSystem.forms.input('uneb_code', 'UNEB Code', '', {
                  placeholder: 'e.g., 536/1, 536/2'
                })}
                ${AIMSDesignSystem.forms.input('exam_papers', 'Number of Exam Papers', '1', {
                  type: 'number',
                  min: '1',
                  max: '4'
                })}
              </div>
              
              <!-- Applicable Levels -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Applicable Levels</label>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                  <label class="flex items-center space-x-2">
                    <input type="checkbox" name="applicable_levels[]" value="s1" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <span class="text-sm text-gray-700">S.1</span>
                  </label>
                  <label class="flex items-center space-x-2">
                    <input type="checkbox" name="applicable_levels[]" value="s2" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <span class="text-sm text-gray-700">S.2</span>
                  </label>
                  <label class="flex items-center space-x-2">
                    <input type="checkbox" name="applicable_levels[]" value="s3" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <span class="text-sm text-gray-700">S.3</span>
                  </label>
                  <label class="flex items-center space-x-2">
                    <input type="checkbox" name="applicable_levels[]" value="s4" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <span class="text-sm text-gray-700">S.4</span>
                  </label>
                </div>
              </div>

              <!-- Compulsory Subject -->
              <div>
                <label class="flex items-center space-x-2">
                  <input type="checkbox" name="is_compulsory" value="1" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                  <span class="text-sm text-gray-700">This is a compulsory subject</span>
                </label>
              </div>



              <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                ${AIMSDesignSystem.forms.button('cancel-subject', 'Cancel', 'secondary', {
                  type: 'button',
                  onclick: 'OLevelSubjectsComponent.closeAddSubjectModal()'
                })}
                ${AIMSDesignSystem.forms.button('save-subject', 'Create Subject', 'primary', {
                  type: 'submit'
                })}
              </div>
            </form>
          </div>
        </div>
      </div>
    `;
  },

  // Initialize O-Level subjects component
  async init() {
    await SubjectsManagementComponents.loadInitialData();
    this.populateSystemAdminFields();
    this.populateSubjectsTable();
    this.initializeEventListeners();
  },

  // Populate system admin fields
  populateSystemAdminFields() {
    const currentAdminId = this.getCurrentAdminId();

    if (currentAdminId) {
      const createdByField = document.getElementById('o_level_created_by_id');
      if (createdByField) createdByField.value = currentAdminId;
    }
  },

  // Get current admin ID from authentication
  getCurrentAdminId() {
    // Use the standardized audit fields utility
    if (window.AuditFieldsUtil) {
      return window.AuditFieldsUtil.getCurrentUserId();
    }

    // Fallback implementation
    try {
      if (window.AIMS && window.AIMS.currentUser && window.AIMS.currentUser.id) {
        return window.AIMS.currentUser.id;
      }
      return 1; // Default to system admin
    } catch (error) {
      console.error('Error getting current admin ID:', error);
      return 1;
    }
  },

  // Get O-Level subjects count
  getOLevelSubjectsCount() {
    const subjects = SubjectsManagementComponents.state.subjects.data || SubjectsManagementComponents.state.subjects;
    return Array.isArray(subjects) ? subjects.filter(s => s.level === 'o_level').length : 0;
  },

  // Get compulsory subjects count
  getCompulsorySubjectsCount() {
    const subjects = SubjectsManagementComponents.state.subjects.data || SubjectsManagementComponents.state.subjects;
    return Array.isArray(subjects) ? subjects.filter(s => s.level === 'o_level' && s.is_compulsory).length : 0;
  },

  // Populate subjects table
  populateSubjectsTable() {
    const tbody = document.getElementById('o-level-subjects-table-body');
    if (!tbody) return;

    const subjects = this.getFilteredSubjects();
    
    if (subjects.length === 0) {
      tbody.innerHTML = `
        <tr>
          <td colspan="7" class="px-6 py-12 text-center text-gray-500">
            <i class="fas fa-book text-4xl mb-4 text-gray-300"></i>
            <p class="text-lg font-medium">No O-Level subjects found</p>
            <p class="text-sm">Add subjects to get started.</p>
          </td>
        </tr>
      `;
      return;
    }

    tbody.innerHTML = subjects.map(subject => `
      <tr class="hover:bg-gray-50">
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="text-sm font-medium text-gray-900">${subject.name}</div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          ${subject.short_name}
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${this.getSubjectTypeBadgeClass(subject.subject_type)}">
            ${subject.subject_type}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          ${subject.uneb_code || '-'}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          ${this.formatApplicableLevels(subject.applicable_levels)}
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="flex items-center space-x-2">
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
              subject.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }">
              ${subject.is_active ? 'Active' : 'Inactive'}
            </span>
            ${subject.is_compulsory ? '<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Compulsory</span>' : ''}
          </div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
          <div class="flex items-center justify-end space-x-2">
            <button onclick="OLevelSubjectsComponent.editSubject(${subject.id})" 
                    class="text-indigo-600 hover:text-indigo-900" title="Edit">
              <i class="fas fa-edit"></i>
            </button>
            <button onclick="OLevelSubjectsComponent.toggleSubjectStatus(${subject.id})" 
                    class="text-yellow-600 hover:text-yellow-900" title="Toggle Status">
              <i class="fas fa-toggle-${subject.is_active ? 'on' : 'off'}"></i>
            </button>
            <button onclick="OLevelSubjectsComponent.deleteSubject(${subject.id})" 
                    class="text-red-600 hover:text-red-900" title="Delete">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </td>
      </tr>
    `).join('');
  },

  // Get filtered subjects
  getFilteredSubjects() {
    const subjectsData = SubjectsManagementComponents.state.subjects.data || SubjectsManagementComponents.state.subjects;
    let subjects = Array.isArray(subjectsData) ? subjectsData.filter(s => s.level === 'o_level') : [];
    
    // Apply search filter
    const searchTerm = document.getElementById('search_subjects')?.value.toLowerCase() || '';
    if (searchTerm) {
      subjects = subjects.filter(subject => 
        subject.name.toLowerCase().includes(searchTerm) ||
        subject.short_name.toLowerCase().includes(searchTerm)
      );
    }

    // Apply subject type filter
    const typeFilter = document.getElementById('filter_subject_type')?.value || '';
    if (typeFilter) {
      subjects = subjects.filter(subject => subject.subject_type === typeFilter);
    }

    // Apply applicable levels filter
    const levelFilter = document.getElementById('filter_applicable_levels')?.value || '';
    if (levelFilter) {
      subjects = subjects.filter(subject => 
        subject.applicable_levels && subject.applicable_levels.includes(levelFilter)
      );
    }

    return subjects;
  },

  // Get subject type badge CSS class
  getSubjectTypeBadgeClass(subjectType) {
    const classes = {
      'Compulsory': 'bg-blue-100 text-blue-800',
      'Language': 'bg-green-100 text-green-800',
      'Practical (pre-vocational)': 'bg-purple-100 text-purple-800',
      'Religious Education': 'bg-yellow-100 text-yellow-800'
    };
    return classes[subjectType] || 'bg-gray-100 text-gray-800';
  },

  // Format applicable levels for display
  formatApplicableLevels(applicableLevels) {
    if (!applicableLevels) return '-';
    return applicableLevels.split(',').map(level => level.toUpperCase()).join(', ');
  },

  // Initialize event listeners
  initializeEventListeners() {
    // Add subject form submission
    const addSubjectForm = document.getElementById('add-o-level-subject-form');
    if (addSubjectForm) {
      addSubjectForm.addEventListener('submit', this.handleAddSubject.bind(this));
    }

    // Search and filter inputs
    const searchInput = document.getElementById('search_subjects');
    if (searchInput) {
      searchInput.addEventListener('input', () => {
        this.populateSubjectsTable();
      });
    }

    ['filter_subject_type', 'filter_applicable_levels'].forEach(filterId => {
      const filterElement = document.getElementById(filterId);
      if (filterElement) {
        filterElement.addEventListener('change', () => {
          this.populateSubjectsTable();
        });
      }
    });
  },

  // Show add subject modal
  showAddSubjectModal() {
    document.getElementById('add-o-level-subject-modal').classList.remove('hidden');
  },

  // Close add subject modal
  closeAddSubjectModal() {
    document.getElementById('add-o-level-subject-modal').classList.add('hidden');
    document.getElementById('add-o-level-subject-form').reset();
  },

  // Handle add subject form submission
  async handleAddSubject(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const data = Object.fromEntries(formData.entries());

    // Ensure system admin fields are populated
    const currentAdminId = this.getCurrentAdminId();
    if (currentAdminId) {
      data.created_by_id = currentAdminId;
    }

    // Get selected applicable levels
    const applicableLevels = Array.from(document.querySelectorAll('input[name="applicable_levels[]"]:checked'))
      .map(input => input.value);

    data.applicable_levels = applicableLevels.join(',');
    data.is_compulsory = data.is_compulsory ? true : false;

    try {
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.forms) {
        window.AIMSDesignSystem.forms.setButtonLoading('save-subject', true);
      }

      // Use the O-Level API service
      const result = await window.SubjectsAPI.oLevel.create(data);

      if (result.success) {
        // Show success notification if available
        if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
          window.AIMSDesignSystem.notifications.show('O-Level subject created successfully!', 'success');
        }
        this.closeAddSubjectModal();
        this.populateSystemAdminFields(); // Re-populate hidden fields
        await SubjectsManagementComponents.loadInitialData();
        this.populateSubjectsTable();
      } else {
        // Show error notification if available
        if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
          window.AIMSDesignSystem.notifications.show(result.message || 'Failed to create subject', 'error');
        }
      }
    } catch (error) {
      console.error('Create subject error:', error);
      if (window.AIMSConfig && window.AIMSConfig.get('development.debugMode')) {
        console.error('Debug: Create subject error details:', error);
      }
      // Show error notification if available
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
        window.AIMSDesignSystem.notifications.show('Failed to create subject', 'error');
      }
    } finally {
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.forms) {
        window.AIMSDesignSystem.forms.setButtonLoading('save-subject', false);
      }
    }
  },

  // Edit subject
  editSubject(subjectId) {
    console.log('Edit subject:', subjectId);
  },

  // Toggle subject status
  async toggleSubjectStatus(subjectId) {
    try {
      // Use the new O-Level API service
      const result = await SubjectsAPI.oLevel.toggleStatus(subjectId);

      if (result.success) {
        if (window.showNotification) {
          window.showNotification('Subject status updated successfully', 'success');
        } else {
          alert('Subject status updated successfully');
        }
        await SubjectsManagementComponents.loadInitialData();
        this.populateSubjectsTable();
      } else {
        if (window.showNotification) {
          window.showNotification(result.message || 'Failed to update subject status', 'error');
        } else {
          alert('Error: ' + (result.message || 'Failed to update subject status'));
        }
      }
    } catch (error) {
      console.error('Toggle status error:', error);
      if (window.showNotification) {
        window.showNotification('Failed to update subject status', 'error');
      } else {
        alert('Error: Failed to update subject status');
      }
    }
  },

  // Delete subject
  async deleteSubject(subjectId) {
    if (!confirm('Are you sure you want to delete this subject? This action cannot be undone.')) {
      return;
    }

    try {
      // Use the new O-Level API service
      const result = await SubjectsAPI.oLevel.delete(subjectId);

      if (result.success) {
        if (window.showNotification) {
          window.showNotification('Subject deleted successfully', 'success');
        } else {
          alert('Subject deleted successfully');
        }
        await SubjectsManagementComponents.loadInitialData();
        this.populateSubjectsTable();
      } else {
        if (window.showNotification) {
          window.showNotification(result.message || 'Failed to delete subject', 'error');
        } else {
          alert('Error: ' + (result.message || 'Failed to delete subject'));
        }
      }
    } catch (error) {
      console.error('Delete error:', error);
      if (window.showNotification) {
        window.showNotification('Failed to delete subject', 'error');
      } else {
        alert('Error: Failed to delete subject');
      }
    }
  }
};

// A-Level Subjects Component
const ALevelSubjectsComponent = {
  // Render A-Level subjects interface
  render() {
    return `
      <div class="space-y-6">
        ${AIMSDesignSystem.layouts.pageHeader(
          'A-Level Subjects',
          'Manage A-Level subjects for Senior 5-6 classes',
          [
            { icon: 'fas fa-graduation-cap', value: this.getALevelSubjectsCount(), label: 'A-Level Subjects', color: 'blue' },
            { icon: 'fas fa-star', value: this.getPrincipalSubjectsCount(), label: 'Principal', color: 'green' }
          ]
        )}

        <!-- Subject Management Actions -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">A-Level Subject Management</h3>
            ${AIMSDesignSystem.forms.button('add-a-level-subject', 'Add New Subject', 'primary', {
              icon: 'fas fa-plus',
              onclick: 'ALevelSubjectsComponent.showAddSubjectModal()'
            })}
          </div>

          <!-- Filters -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            ${AIMSDesignSystem.forms.input('search_a_level_subjects', 'Search Subjects', '', {
              placeholder: 'Search by name or code...',
              icon: 'fas fa-search'
            })}
            ${AIMSDesignSystem.forms.select('filter_a_level_subject_type', 'Subject Type', [
              { value: '', label: 'All Types' },
              { value: 'Compulsory', label: 'Compulsory' },
              { value: 'Language', label: 'Language' },
              { value: 'Practical (pre-vocational)', label: 'Practical (pre-vocational)' },
              { value: 'Religious Education', label: 'Religious Education' }
            ], '')}
            ${AIMSDesignSystem.forms.select('filter_stream_type', 'Stream Type', [
              { value: '', label: 'All Streams' },
              { value: 'Arts', label: 'Arts' },
              { value: 'Sciences', label: 'Sciences' },
              { value: 'Both', label: 'Both' }
            ], '')}
          </div>
        </div>

        <!-- Subjects Table -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">A-Level Subjects</h3>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UNEB Code</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Papers</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody id="a-level-subjects-table-body" class="bg-white divide-y divide-gray-200">
                <!-- Subjects will be populated here -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    `;
  },

  // Initialize A-Level subjects component
  async init() {
    await SubjectsManagementComponents.loadInitialData();
    this.populateSystemAdminFields();
    this.populateSubjectsTable();
    this.initializeEventListeners();
  },

  // Populate system admin fields
  populateSystemAdminFields() {
    const currentAdminId = this.getCurrentAdminId();

    if (currentAdminId) {
      const createdByField = document.getElementById('a_level_created_by_id');
      if (createdByField) createdByField.value = currentAdminId;
    }
  },

  // Get current admin ID from authentication (same as O-Level component)
  getCurrentAdminId() {
    try {
      // Try to get from localStorage first
      const adminData = localStorage.getItem('aims_admin_data');
      if (adminData) {
        const parsed = JSON.parse(adminData);
        return parsed.id || parsed.user_id;
      }

      // Try to get from JWT token
      const token = localStorage.getItem('aims_token');
      if (token) {
        const payload = JSON.parse(atob(token.split('.')[1]));
        return payload.id || payload.user_id || payload.userId;
      }

      // Fallback: try to get from global auth state
      if (window.AuthManager && window.AuthManager.getCurrentUser) {
        const user = window.AuthManager.getCurrentUser();
        return user?.id;
      }

      return null;
    } catch (error) {
      console.error('Error getting current admin ID:', error);
      return null;
    }
  },

  // Get A-Level subjects count
  getALevelSubjectsCount() {
    return SubjectsManagementComponents.state.subjects.data?.filter(s => s.level === 'a_level').length || 0;
  },

  // Get principal subjects count
  getPrincipalSubjectsCount() {
    return SubjectsManagementComponents.state.subjects.data?.filter(s => s.level === 'a_level' && s.is_compulsory).length || 0;
  },

  // Populate subjects table
  populateSubjectsTable() {
    const tbody = document.getElementById('a-level-subjects-table-body');
    if (!tbody) return;

    const subjects = this.getFilteredSubjects();

    if (subjects.length === 0) {
      tbody.innerHTML = `
        <tr>
          <td colspan="7" class="px-6 py-12 text-center text-gray-500">
            <i class="fas fa-graduation-cap text-4xl mb-4 text-gray-300"></i>
            <p class="text-lg font-medium">No A-Level subjects found</p>
            <p class="text-sm">Add subjects to get started.</p>
          </td>
        </tr>
      `;
      return;
    }

    tbody.innerHTML = subjects.map(subject => `
      <tr class="hover:bg-gray-50">
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="text-sm font-medium text-gray-900">${subject.name}</div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          ${subject.short_name}
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${this.getSubjectTypeBadgeClass(subject.subject_type)}">
            ${subject.subject_type}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          ${subject.uneb_code || '-'}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          ${subject.exam_papers || 1}
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="flex items-center space-x-2">
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
              subject.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }">
              ${subject.is_active ? 'Active' : 'Inactive'}
            </span>
            ${subject.is_compulsory ? '<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Principal</span>' : ''}
          </div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
          <div class="flex items-center justify-end space-x-2">
            <button onclick="ALevelSubjectsComponent.editSubject(${subject.id})"
                    class="text-indigo-600 hover:text-indigo-900" title="Edit">
              <i class="fas fa-edit"></i>
            </button>
            <button onclick="ALevelSubjectsComponent.toggleSubjectStatus(${subject.id})"
                    class="text-yellow-600 hover:text-yellow-900" title="Toggle Status">
              <i class="fas fa-toggle-${subject.is_active ? 'on' : 'off'}"></i>
            </button>
            <button onclick="ALevelSubjectsComponent.deleteSubject(${subject.id})"
                    class="text-red-600 hover:text-red-900" title="Delete">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </td>
      </tr>
    `).join('');
  },

  // Get filtered subjects
  getFilteredSubjects() {
    let subjects = SubjectsManagementComponents.state.subjects.data?.filter(s => s.level === 'a_level') || [];

    // Apply search filter
    const searchTerm = document.getElementById('search_a_level_subjects')?.value.toLowerCase() || '';
    if (searchTerm) {
      subjects = subjects.filter(subject =>
        subject.name.toLowerCase().includes(searchTerm) ||
        subject.short_name.toLowerCase().includes(searchTerm)
      );
    }

    // Apply subject type filter
    const typeFilter = document.getElementById('filter_a_level_subject_type')?.value || '';
    if (typeFilter) {
      subjects = subjects.filter(subject => subject.subject_type === typeFilter);
    }

    return subjects;
  },

  // Get subject type badge CSS class
  getSubjectTypeBadgeClass(subjectType) {
    const classes = {
      'Compulsory': 'bg-blue-100 text-blue-800',
      'Language': 'bg-green-100 text-green-800',
      'Practical (pre-vocational)': 'bg-purple-100 text-purple-800',
      'Religious Education': 'bg-yellow-100 text-yellow-800'
    };
    return classes[subjectType] || 'bg-gray-100 text-gray-800';
  },

  // Initialize event listeners
  initializeEventListeners() {
    // Search and filter inputs
    const searchInput = document.getElementById('search_a_level_subjects');
    if (searchInput) {
      searchInput.addEventListener('input', () => {
        this.populateSubjectsTable();
      });
    }

    ['filter_a_level_subject_type', 'filter_stream_type'].forEach(filterId => {
      const filterElement = document.getElementById(filterId);
      if (filterElement) {
        filterElement.addEventListener('change', () => {
          this.populateSubjectsTable();
        });
      }
    });
  },

  // Show add subject modal
  showAddSubjectModal() {
    console.log('Show add A-Level subject modal');
  },

  // Edit subject
  editSubject(subjectId) {
    console.log('Edit A-Level subject:', subjectId);
  },

  // Toggle subject status
  async toggleSubjectStatus(subjectId) {
    try {
      // Use the new A-Level API service
      const result = await SubjectsAPI.aLevel.toggleStatus(subjectId);

      if (result.success) {
        if (window.showNotification) {
          window.showNotification('A-Level subject status updated successfully', 'success');
        } else {
          alert('A-Level subject status updated successfully');
        }
        await SubjectsManagementComponents.loadInitialData();
        this.populateSubjectsTable();
      } else {
        if (window.showNotification) {
          window.showNotification(result.message || 'Failed to update subject status', 'error');
        } else {
          alert('Error: ' + (result.message || 'Failed to update subject status'));
        }
      }
    } catch (error) {
      console.error('Toggle status error:', error);
      if (window.showNotification) {
        window.showNotification('Failed to update subject status', 'error');
      } else {
        alert('Error: Failed to update subject status');
      }
    }
  },

  // Delete subject
  async deleteSubject(subjectId) {
    if (!confirm('Are you sure you want to delete this subject? This action cannot be undone.')) {
      return;
    }

    try {
      // Use the new A-Level API service
      const result = await SubjectsAPI.aLevel.delete(subjectId);

      if (result.success) {
        if (window.showNotification) {
          window.showNotification('A-Level subject deleted successfully', 'success');
        } else {
          alert('A-Level subject deleted successfully');
        }
        await SubjectsManagementComponents.loadInitialData();
        this.populateSubjectsTable();
      } else {
        if (window.showNotification) {
          window.showNotification(result.message || 'Failed to delete subject', 'error');
        } else {
          alert('Error: ' + (result.message || 'Failed to delete subject'));
        }
      }
    } catch (error) {
      console.error('Delete error:', error);
      if (window.showNotification) {
        window.showNotification('Failed to delete subject', 'error');
      } else {
        alert('Error: Failed to delete subject');
      }
    }
  }
};

// A-Level Combinations Component
const ALevelCombinationsComponent = {
  // Render A-Level combinations interface
  render() {
    return `
      <div class="space-y-6">
        ${AIMSDesignSystem.layouts.pageHeader(
          'A-Level Combinations',
          'Manage subject combinations for A-Level students',
          [
            { icon: 'fas fa-layer-group', value: this.getCombinationsCount(), label: 'Total Combinations', color: 'blue' },
            { icon: 'fas fa-check-circle', value: this.getActiveCombinationsCount(), label: 'Active', color: 'green' }
          ]
        )}

        <!-- Combination Management Actions -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">Combination Management</h3>
            ${AIMSDesignSystem.forms.button('add-combination', 'Add New Combination', 'primary', {
              icon: 'fas fa-plus',
              onclick: 'ALevelCombinationsComponent.showAddCombinationModal()'
            })}
          </div>

          <!-- Filters -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            ${AIMSDesignSystem.forms.input('search_combinations', 'Search Combinations', '', {
              placeholder: 'Search by name or code...',
              icon: 'fas fa-search'
            })}
            ${AIMSDesignSystem.forms.select('filter_combination_stream', 'Stream Type', [
              { value: '', label: 'All Streams' },
              { value: 'Arts', label: 'Arts' },
              { value: 'Sciences', label: 'Sciences' }
            ], '')}
            ${AIMSDesignSystem.forms.select('filter_combination_status', 'Status', [
              { value: '', label: 'All Status' },
              { value: 'active', label: 'Active' },
              { value: 'inactive', label: 'Inactive' }
            ], '')}
          </div>
        </div>

        <!-- Combinations Grid -->
        <div id="combinations-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <!-- Combinations will be populated here -->
        </div>
      </div>

      <!-- Add Combination Modal -->
      <div id="add-combination-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-xl bg-white">
          <div class="mt-3">
            <div class="flex items-center justify-between pb-4 border-b border-gray-200">
              <h3 class="text-lg font-semibold text-gray-900">Add New A-Level Combination</h3>
              <button onclick="ALevelCombinationsComponent.closeAddCombinationModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
              </button>
            </div>
            <form id="add-combination-form" class="mt-6 space-y-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                ${AIMSDesignSystem.forms.input('combination_name', 'Combination Name', '', {
                  required: true,
                  placeholder: 'e.g., History, Economics, Geography'
                })}
                ${AIMSDesignSystem.forms.input('combination_code', 'Combination Code', '', {
                  required: true,
                  placeholder: 'e.g., HEG'
                })}
                ${AIMSDesignSystem.forms.select('stream_type', 'Stream Type', [
                  { value: '', label: 'Select Stream Type' },
                  { value: 'Arts', label: 'Arts' },
                  { value: 'Sciences', label: 'Sciences' }
                ], '', { required: true })}
                ${AIMSDesignSystem.forms.input('max_students', 'Maximum Students', '', {
                  type: 'number',
                  min: '1',
                  placeholder: 'Optional maximum enrollment'
                })}
              </div>

              <!-- Subject Selection -->
              <div class="space-y-6">
                <!-- A-Level Subject Selection Rules -->
                <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
                  <h4 class="font-semibold text-blue-900 mb-2 flex items-center">
                    <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                    A-Level Subject Selection Rules
                  </h4>
                  <p class="text-sm text-blue-800">
                    Select subjects to include in this combination. Students will choose from these subjects according to A-Level rules.
                  </p>
                </div>

                <!-- Principal Subjects Selection -->
                <div class="bg-green-50 rounded-lg p-4 border border-green-200">
                  <h4 class="font-semibold text-green-900 mb-3 flex items-center">
                    <i class="fas fa-star text-green-600 mr-2"></i>
                    Principal Subjects (Select subjects to include)
                  </h4>
                  <div id="principal-subjects-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    <!-- Principal subjects will be populated here -->
                  </div>
                </div>

                <!-- Subsidiary Subjects Selection -->
                <div class="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
                  <h4 class="font-semibold text-yellow-900 mb-3 flex items-center">
                    <i class="fas fa-graduation-cap text-yellow-600 mr-2"></i>
                    Subsidiary Subjects (Select subjects to include)
                  </h4>
                  <div id="subsidiary-subjects-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    <!-- Subsidiary subjects will be populated here -->
                  </div>
                </div>

                <!-- General Paper (Automatic) -->
                <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <h4 class="font-semibold text-gray-900 mb-2 flex items-center">
                    <i class="fas fa-check-circle text-gray-600 mr-2"></i>
                    Compulsory Subject
                  </h4>
                  <p class="text-sm text-gray-700">
                    <strong>General Paper (GP)</strong> - Automatically included for all A-Level combinations
                  </p>
                </div>
              </div>



              <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                ${AIMSDesignSystem.forms.button('cancel-combination', 'Cancel', 'secondary', {
                  type: 'button',
                  onclick: 'ALevelCombinationsComponent.closeAddCombinationModal()'
                })}
                ${AIMSDesignSystem.forms.button('save-combination', 'Create Combination', 'primary', {
                  type: 'submit'
                })}
              </div>
            </form>
          </div>
        </div>
      </div>
    `;
  },

  // Initialize A-Level combinations component
  async init() {
    await SubjectsManagementComponents.loadInitialData();
    this.populateCombinationsGrid();
    this.initializeEventListeners();
  },

  // Get combinations count
  getCombinationsCount() {
    return SubjectsManagementComponents.state.combinations.data?.length || 0;
  },

  // Get active combinations count
  getActiveCombinationsCount() {
    return SubjectsManagementComponents.state.combinations.data?.filter(c => c.is_active).length || 0;
  },

  // Populate combinations grid
  populateCombinationsGrid() {
    const grid = document.getElementById('combinations-grid');
    if (!grid) return;

    const combinations = this.getFilteredCombinations();

    if (combinations.length === 0) {
      grid.innerHTML = `
        <div class="col-span-full text-center py-12">
          <i class="fas fa-layer-group text-4xl mb-4 text-gray-300"></i>
          <p class="text-lg font-medium text-gray-500">No combinations found</p>
          <p class="text-sm text-gray-400">Create new combinations to get started.</p>
        </div>
      `;
      return;
    }

    grid.innerHTML = combinations.map(combination => `
      <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div class="flex items-start justify-between mb-4">
          <div>
            <h3 class="text-lg font-semibold text-gray-900">${combination.name}</h3>
            <p class="text-sm text-gray-500">${combination.code}</p>
          </div>
          <div class="flex items-center space-x-2">
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${this.getStreamTypeBadgeClass(combination.stream_type)}">
              ${combination.stream_type}
            </span>
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
              combination.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }">
              ${combination.is_active ? 'Active' : 'Inactive'}
            </span>
          </div>
        </div>



        <div class="mb-4 space-y-3">
          <!-- Principal Subjects -->
          <div>
            <h4 class="text-sm font-medium text-gray-700 mb-2">
              <i class="fas fa-star text-green-600 mr-1"></i>Principal Subjects:
            </h4>
            <div class="flex flex-wrap gap-1">
              ${this.getCombinationPrincipalSubjects(combination).map(subject => `
                <span class="inline-flex px-2 py-1 text-xs font-medium rounded bg-green-100 text-green-800">
                  ${subject.short_name || subject.name}
                </span>
              `).join('')}
            </div>
          </div>

          <!-- Subsidiary Subject -->
          <div>
            <h4 class="text-sm font-medium text-gray-700 mb-2">
              <i class="fas fa-graduation-cap text-yellow-600 mr-1"></i>Subsidiary Subject:
            </h4>
            <div class="flex flex-wrap gap-1">
              ${combination.subsidiary_subject_name ? `
                <span class="inline-flex px-2 py-1 text-xs font-medium rounded bg-yellow-100 text-yellow-800">
                  ${combination.subsidiary_subject_short_name || combination.subsidiary_subject_name}
                </span>
              ` : '<span class="text-xs text-gray-500">Not specified</span>'}
            </div>
          </div>

          <!-- General Paper (Always included) -->
          <div>
            <h4 class="text-sm font-medium text-gray-700 mb-2">
              <i class="fas fa-check-circle text-gray-600 mr-1"></i>Compulsory:
            </h4>
            <div class="flex flex-wrap gap-1">
              <span class="inline-flex px-2 py-1 text-xs font-medium rounded bg-gray-100 text-gray-800">
                General Paper (GP)
              </span>
            </div>
          </div>
        </div>

        <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
          <span>Students: ${combination.enrolled_students || 0}</span>
          ${combination.max_students ? `<span>Max: ${combination.max_students}</span>` : ''}
        </div>

        <div class="flex items-center justify-end space-x-2 pt-4 border-t border-gray-200">
          <button onclick="ALevelCombinationsComponent.viewCombination(${combination.id})"
                  class="text-blue-600 hover:text-blue-900 text-sm" title="View Details">
            <i class="fas fa-eye mr-1"></i>View
          </button>
          <button onclick="ALevelCombinationsComponent.editCombination(${combination.id})"
                  class="text-indigo-600 hover:text-indigo-900 text-sm" title="Edit">
            <i class="fas fa-edit mr-1"></i>Edit
          </button>
          <button onclick="ALevelCombinationsComponent.toggleCombinationStatus(${combination.id})"
                  class="text-yellow-600 hover:text-yellow-900 text-sm" title="Toggle Status">
            <i class="fas fa-toggle-${combination.is_active ? 'on' : 'off'} mr-1"></i>Toggle
          </button>
          <button onclick="ALevelCombinationsComponent.deleteCombination(${combination.id})"
                  class="text-red-600 hover:text-red-900 text-sm" title="Delete">
            <i class="fas fa-trash mr-1"></i>Delete
          </button>
        </div>
      </div>
    `).join('');
  },

  // Get filtered combinations
  getFilteredCombinations() {
    let combinations = SubjectsManagementComponents.state.combinations.data || [];

    // Apply search filter
    const searchTerm = document.getElementById('search_combinations')?.value.toLowerCase() || '';
    if (searchTerm) {
      combinations = combinations.filter(combination =>
        combination.name.toLowerCase().includes(searchTerm) ||
        combination.code.toLowerCase().includes(searchTerm)
      );
    }

    // Apply stream filter
    const streamFilter = document.getElementById('filter_combination_stream')?.value || '';
    if (streamFilter) {
      combinations = combinations.filter(combination => combination.stream_type === streamFilter);
    }

    // Apply status filter
    const statusFilter = document.getElementById('filter_combination_status')?.value || '';
    if (statusFilter) {
      const isActive = statusFilter === 'active';
      combinations = combinations.filter(combination => combination.is_active === isActive);
    }

    return combinations;
  },

  // Get stream type badge CSS class
  getStreamTypeBadgeClass(streamType) {
    const classes = {
      'Arts': 'bg-purple-100 text-purple-800',
      'Sciences': 'bg-blue-100 text-blue-800'
    };
    return classes[streamType] || 'bg-gray-100 text-gray-800';
  },

  // Get combination subjects
  getCombinationSubjects(subjectsJson) {
    if (!subjectsJson) return [];

    try {
      const subjectIds = JSON.parse(subjectsJson);
      const allSubjects = SubjectsManagementComponents.state.subjects.data || [];
      return allSubjects.filter(s => subjectIds.includes(s.id));
    } catch (error) {
      return [];
    }
  },

  // Get combination principal subjects
  getCombinationPrincipalSubjects(combination) {
    // For now, we'll use the existing subjects field until the API is updated
    // In the future, this should come from the a_level_combination_subjects table
    const subjects = this.getCombinationSubjects(combination.subjects);

    // Filter for principal subjects only
    return subjects.filter(s =>
      s.subject_type === 'Principal' || s.subject_type === 'principal'
    );
  },

  // Initialize event listeners
  initializeEventListeners() {
    // Add combination form submission
    const addCombinationForm = document.getElementById('add-combination-form');
    if (addCombinationForm) {
      addCombinationForm.addEventListener('submit', this.handleAddCombination.bind(this));
    }

    // Search and filter inputs
    const searchInput = document.getElementById('search_combinations');
    if (searchInput) {
      searchInput.addEventListener('input', () => {
        this.populateCombinationsGrid();
      });
    }

    ['filter_combination_stream', 'filter_combination_status'].forEach(filterId => {
      const filterElement = document.getElementById(filterId);
      if (filterElement) {
        filterElement.addEventListener('change', () => {
          this.populateCombinationsGrid();
        });
      }
    });
  },

  // Show add combination modal
  showAddCombinationModal() {
    this.populateSubjectsForCombination();
    document.getElementById('add-combination-modal').classList.remove('hidden');
  },

  // Close add combination modal
  closeAddCombinationModal() {
    document.getElementById('add-combination-modal').classList.add('hidden');
    document.getElementById('add-combination-form').reset();
  },

  // Populate subjects for combination selection
  async populateSubjectsForCombination() {
    try {
      // Load A-Level subjects if not already loaded
      if (!SubjectsManagementComponents.state.subjects.data) {
        await SubjectsManagementComponents.loadInitialData();
      }

      const aLevelSubjects = SubjectsManagementComponents.state.subjects.data?.filter(s => s.level === 'a_level') || [];

      // Separate subjects by type
      const principalSubjects = aLevelSubjects.filter(s =>
        s.subject_type === 'Principal' || s.subject_type === 'principal'
      );
      const subsidiarySubjects = aLevelSubjects.filter(s =>
        s.subject_type === 'Subsidiary' || s.subject_type === 'subsidiary'
      );

      // Populate principal subjects
      this.populatePrincipalSubjects(principalSubjects);

      // Populate subsidiary subjects
      this.populateSubsidiarySubjects(subsidiarySubjects);

      // Add event listeners for validation
      this.addCombinationValidationListeners();

    } catch (error) {
      console.error('Error populating subjects for combination:', error);
    }
  },

  // Populate principal subjects section
  populatePrincipalSubjects(subjects) {
    const container = document.getElementById('principal-subjects-container');
    if (!container) return;

    container.innerHTML = subjects.map(subject => `
      <div class="flex items-center space-x-3 p-3 bg-white border border-green-200 rounded-lg hover:border-green-300 transition-colors">
        <input type="checkbox"
               id="principal_subject_${subject.id}"
               name="principal_subjects[]"
               value="${subject.id}"
               class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
        <label for="principal_subject_${subject.id}" class="flex-1 cursor-pointer">
          <div class="font-medium text-gray-900 text-sm">${subject.name}</div>
          <div class="text-xs text-gray-500">${subject.short_name || subject.code || ''}</div>
          <span class="inline-block text-xs text-green-600 font-medium bg-green-100 px-2 py-1 rounded mt-1">Principal</span>
        </label>
      </div>
    `).join('');
  },

  // Populate subsidiary subjects section
  populateSubsidiarySubjects(subjects) {
    const container = document.getElementById('subsidiary-subjects-container');
    if (!container) return;

    container.innerHTML = subjects.map(subject => `
      <div class="flex items-center space-x-3 p-3 bg-white border border-yellow-200 rounded-lg hover:border-yellow-300 transition-colors">
        <input type="checkbox"
               id="subsidiary_subject_${subject.id}"
               name="subsidiary_subjects[]"
               value="${subject.id}"
               class="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded">
        <label for="subsidiary_subject_${subject.id}" class="flex-1 cursor-pointer">
          <div class="font-medium text-gray-900 text-sm">${subject.name}</div>
          <div class="text-xs text-gray-500">${subject.short_name || subject.code || ''}</div>
          <span class="inline-block text-xs text-yellow-600 font-medium bg-yellow-100 px-2 py-1 rounded mt-1">Subsidiary</span>
        </label>
      </div>
    `).join('');
  },

  // Add validation listeners for combination selection
  addCombinationValidationListeners() {
    // No strict validation - just like O-Level side
    // Users can select any subjects they want to include in the combination
    console.log('✅ A-Level combination subject selection ready');
  },

  // Handle add combination form submission
  async handleAddCombination(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const data = Object.fromEntries(formData.entries());

    // Get selected principal subjects (exactly 3 required)
    const selectedPrincipal = Array.from(document.querySelectorAll('input[name="principal_subjects[]"]:checked'))
      .map(input => parseInt(input.value));

    // Get selected subsidiary subject (exactly 1 required)
    const selectedSubsidiary = document.querySelector('input[name="subsidiary_subject"]:checked');

    // Validate selection
    if (selectedPrincipal.length !== 3) {
      AIMSDesignSystem.notifications.show('Please select exactly 3 principal subjects', 'error');
      return;
    }

    if (!selectedSubsidiary) {
      AIMSDesignSystem.notifications.show('Please select exactly 1 subsidiary subject', 'error');
      return;
    }

    // Prepare data for API
    data.principal_subjects = selectedPrincipal;
    data.subsidiary_subject_id = parseInt(selectedSubsidiary.value);

    try {
      AIMSDesignSystem.forms.setButtonLoading('save-combination', true);

      // Use the API service
      const result = await CombinationsAPI.create(data);

      if (result.success) {
        AIMSDesignSystem.notifications.show('A-Level combination created successfully!', 'success');
        this.closeAddCombinationModal();
        await SubjectsManagementComponents.loadInitialData();
        this.populateCombinationsGrid();
      } else {
        AIMSDesignSystem.notifications.show(result.message || 'Failed to create combination', 'error');
      }
    } catch (error) {
      console.error('Create combination error:', error);
      AIMSDesignSystem.notifications.show('Failed to create combination', 'error');
    } finally {
      AIMSDesignSystem.forms.setButtonLoading('save-combination', false);
    }
  },

  // View combination details
  viewCombination(combinationId) {
    console.log('View combination:', combinationId);
  },

  // Edit combination
  editCombination(combinationId) {
    console.log('Edit combination:', combinationId);
  },

  // Toggle combination status
  async toggleCombinationStatus(combinationId) {
    try {
      // Use the new API service
      const result = await CombinationsAPI.toggleStatus(combinationId);

      if (result.success) {
        AIMSDesignSystem.notifications.show('Combination status updated successfully', 'success');
        await SubjectsManagementComponents.loadInitialData();
        this.populateCombinationsGrid();
      } else {
        AIMSDesignSystem.notifications.show(result.message || 'Failed to update combination status', 'error');
      }
    } catch (error) {
      console.error('Toggle status error:', error);
      AIMSDesignSystem.notifications.show('Failed to update combination status', 'error');
    }
  },

  // Delete combination
  async deleteCombination(combinationId) {
    if (!confirm('Are you sure you want to delete this combination? This action cannot be undone.')) {
      return;
    }

    try {
      // Use the new API service
      const result = await CombinationsAPI.delete(combinationId);

      if (result.success) {
        AIMSDesignSystem.notifications.show('Combination deleted successfully', 'success');
        await SubjectsManagementComponents.loadInitialData();
        this.populateCombinationsGrid();
      } else {
        AIMSDesignSystem.notifications.show(result.message || 'Failed to delete combination', 'error');
      }
    } catch (error) {
      console.error('Delete error:', error);
      AIMSDesignSystem.notifications.show('Failed to delete combination', 'error');
    }
  }
};

// Export components to global scope
window.OLevelSubjectsComponent = OLevelSubjectsComponent;
window.ALevelSubjectsComponent = ALevelSubjectsComponent;
window.ALevelCombinationsComponent = ALevelCombinationsComponent;
window.SubjectsManagementComponents = SubjectsManagementComponents;
